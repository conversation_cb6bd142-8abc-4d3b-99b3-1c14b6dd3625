#include "Encoder.h"

// 全局编码器变量
static encoder_t encoder = {0};

/**
 * @brief 编码器初始化
 */
void Encoder_Init(void)
{
    encoder.count = 0;
    encoder.state = ENCODER_IDLE;
    encoder.last_a = HAL_GPIO_ReadPin(ENCODER_PORT, ENCODER_A_PIN);
    encoder.last_b = HAL_GPIO_ReadPin(ENCODER_PORT, ENCODER_B_PIN);
}

/**
 * @brief 编码器中断处理函数
 * @param GPIO_Pin 触发中断的引脚
 */
void Encoder_IRQ_Handler(uint16_t GPIO_Pin)
{
    uint8_t current_a = HAL_GPIO_ReadPin(ENCODER_PORT, ENCODER_A_PIN);
    uint8_t current_b = HAL_GPIO_ReadPin(ENCODER_PORT, ENCODER_B_PIN);

    // 四倍频解码算法
    if (GPIO_Pin == ENCODER_A_PIN) // A相中断
    {
        if (current_a != encoder.last_a) // A相状态改变
        {
            if (current_a == 0) // A相下降沿
            {
                if (current_b == 1) // B相为高电平，顺时针
                {
                    encoder.count++;
                    encoder.state = ENCODER_CW;
                }
                else // B相为低电平，逆时针
                {
                    encoder.count--;
                    encoder.state = ENCODER_CCW;
                }
            }
            encoder.last_a = current_a;
        }
    }
    else if (GPIO_Pin == ENCODER_B_PIN) // B相中断
    {
        if (current_b != encoder.last_b) // B相状态改变
        {
            if (current_b == 0) // B相下降沿
            {
                if (current_a == 0) // A相为低电平，顺时针
                {
                    encoder.count++;
                    encoder.state = ENCODER_CW;
                }
                else // A相为高电平，逆时针
                {
                    encoder.count--;
                    encoder.state = ENCODER_CCW;
                }
            }
            encoder.last_b = current_b;
        }
    }
}

/**
 * @brief 获取编码器计数值
 * @return 编码器计数值
 */
int32_t Encoder_GetCount(void)
{
    return encoder.count;
}

/**
 * @brief 重置编码器计数值
 */
void Encoder_ResetCount(void)
{
    encoder.count = 0;
}

/**
 * @brief 获取编码器状态
 * @return 编码器状态
 */
encoder_state_t Encoder_GetState(void)
{
    return encoder.state;
}

/**
 * @brief 编码器任务函数，用于任务调度器
 */
void Encoder_Task(void)
{
    // 清除状态，避免状态一直保持
    if (encoder.state != ENCODER_IDLE)
    {
        encoder.state = ENCODER_IDLE;
    }
}
