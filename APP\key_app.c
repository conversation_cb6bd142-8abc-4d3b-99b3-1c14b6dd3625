#include "key_app.h"
#include "RTE_Components.h"             // Component selection

uint8_t key_val,key_old,key_down,key_up;

uint8_t key_read(void) // 读取按键状态
{
	uint8_t temp = 0;

	if(HAL_GPIO_ReadPin(GPIOA,GPIO_PIN_0) == GPIO_PIN_RESET)temp = 1;

	return temp;
}



void key_task(void) // 按键任务处理函数
{
	key_val = key_read();
	key_down = key_val & (key_old ^ key_val);
	key_up = key_val & (key_old ^ key_val);
	key_old = key_val;
}



