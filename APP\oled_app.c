#include "oled_app.h"

/**
 * @brief OLED应用层初始化
 */
void OLED_App_Init(void)
{
    OLED_Init();                    // 初始化OLED硬件
    OLED_Clear();                   // 清屏
    OLED_App_DisplayHello();        // 显示Hello信息
}

/**
 * @brief 在OLED第一行显示Hello
 */
void OLED_App_DisplayHello(void)
{
    OLED_ShowStr(0, 0, "Hello", 16); // 在第一行显示Hello，使用16号字体
}

/**
 * @brief OLED显示更新函数
 */
void OLED_App_Update(void)
{
    // 可在此添加其他显示更新逻辑
    // 例如：显示时间、传感器数据等
}