#include "oled_app.h"

void OLED_App_Init(void)
{
    OLED_Init(); // 初始化OLED硬件
    OLED_Clear(); // 清屏
}

void OLED_task(void) // 任务调度器调用的OLED任务
{
    static int32_t last_count = 0;
    static uint8_t first_run = 1;
    int32_t current_count = Encoder_GetCount();

    // 首次运行或计数值改变时更新显示
    if (first_run || current_count != last_count)
    {
        OLED_Clear(); // 清屏
        OLED_ShowStr(0, 0, "Encoder Test", 10); // 第一行显示标题
        OLED_ShowStr(0, 2, "Count:", 10); // 第三行显示Count:
        OLED_ShowNum(48, 2, current_count, 5, 10); // 显示编码器计数值

        last_count = current_count;
        first_run = 0;
    }
}
