#ifndef ENCODER_H
#define ENCODER_H

#include "main.h"
#include "gpio.h"
#include <stdint.h>

// 旋转编码器引脚定义
#define ENCODER_A_PIN    GPIO_PIN_3  // PB3
#define ENCODER_B_PIN    GPIO_PIN_4  // PB4

// 旋转编码器状态
typedef enum {
    ENCODER_IDLE = 0,    // 空闲状态
    ENCODER_CW,          // 顺时针旋转
    ENCODER_CCW          // 逆时针旋转
} encoder_state_t;

// 旋转编码器数据结构
typedef struct {
    int32_t count;       // 编码器计数值
    encoder_state_t state; // 当前状态
    uint8_t last_a;      // A相上次状态
    uint8_t last_b;      // B相上次状态
} encoder_t;

// 函数声明
void Encoder_Init(void);                    // 编码器初始化
void Encoder_IRQ_Handler(uint16_t GPIO_Pin); // 中断处理函数
int32_t Encoder_GetCount(void);             // 获取编码器计数
void Encoder_ResetCount(void);              // 重置编码器计数
encoder_state_t Encoder_GetState(void);     // 获取编码器状态
void Encoder_Task(void);                    // 编码器任务函数
uint32_t Encoder_GetInterruptCount(void);   // 获取中断计数（调试用）

#endif
