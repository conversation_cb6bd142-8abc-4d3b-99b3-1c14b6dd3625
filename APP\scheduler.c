#include "scheduler.h"

// 全锟街憋拷锟斤拷锟斤拷锟斤拷锟节存储锟斤拷锟斤拷锟斤拷锟斤拷
uint8_t task_num;

typedef struct {
    void (*task_func)(void);
    uint32_t rate_ms;
    uint32_t last_run;
} task_t;

// 锟斤拷态锟斤拷锟斤拷锟斤拷锟介，每锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷锟街达拷锟斤拷锟斤拷冢锟斤拷锟斤拷耄╋拷锟斤拷洗锟斤拷锟斤拷锟绞憋拷洌拷锟斤拷耄�
static task_t scheduler_task[] =
{
     {key_task,  1,  0}  // 鎸夐敭浠诲姟锛�1ms鍛ㄦ湡
    ,{OLED_task, 100, 0} // OLED浠诲姟锛�100ms鍛ㄦ湡
//    ,{btn_task,  5,  0}
//    ,{uart_task, 5,  0}
};

//锟斤拷锟斤拷锟斤拷锟斤拷始锟斤拷锟斤拷锟斤拷:锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷锟皆拷馗锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷锟芥储锟斤拷 task_num 锟斤拷
void scheduler_init(void)
{
    // 锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷锟皆拷馗锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷锟芥储锟斤拷 task_num 锟斤拷
    task_num = sizeof(scheduler_task) / sizeof(task_t);
}

//锟斤拷锟斤拷锟斤拷锟斤拷锟叫猴拷锟斤拷:锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷锟介，锟斤拷锟斤拷欠锟斤拷锟斤拷锟斤拷锟斤拷锟揭达拷小锟斤拷锟斤拷锟斤拷前时锟斤拷锟窖撅拷锟斤拷锟斤拷锟斤拷锟斤拷锟街达拷锟斤拷锟斤拷冢锟斤拷锟街达拷懈锟斤拷锟斤拷癫⒏锟斤拷锟斤拷洗锟斤拷锟斤拷锟绞憋拷锟�
void scheduler_run(void)
{
    // 锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷锟叫碉拷锟斤拷锟斤拷锟斤拷锟斤拷
    for (uint8_t i = 0; i < task_num; i++)
    {
        // 锟斤拷取锟斤拷前锟斤拷系统时锟戒（锟斤拷锟诫）
        uint32_t now_time = HAL_GetTick();
        // 锟斤拷榈鼻笆憋拷锟斤拷欠锟斤到锟斤拷锟斤拷锟街达拷锟绞憋拷锟�
        if (now_time >= scheduler_task[i].rate_ms + scheduler_task[i].last_run)
        {
            // 锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷洗锟斤拷锟斤拷锟绞憋拷锟轿拷锟角笆憋拷锟�
            scheduler_task[i].last_run = now_time;

            // 执锟斤拷锟斤拷锟斤拷锟斤拷
            scheduler_task[i].task_func();
        }
    }
}


