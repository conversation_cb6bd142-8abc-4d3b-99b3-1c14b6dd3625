Component: ARM Compiler 5.06 update 6 (build 750) Tool: armlink [4d35ed]

==============================================================================

Section Cross References

    startup_stm32g474xx.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32g474xx.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32g474xx.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32g474xx.o(RESET) refers to startup_stm32g474xx.o(STACK) for __initial_sp
    startup_stm32g474xx.o(RESET) refers to startup_stm32g474xx.o(.text) for Reset_Handler
    startup_stm32g474xx.o(RESET) refers to stm32g4xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32g474xx.o(RESET) refers to stm32g4xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32g474xx.o(RESET) refers to stm32g4xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32g474xx.o(RESET) refers to stm32g4xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32g474xx.o(RESET) refers to stm32g4xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32g474xx.o(RESET) refers to stm32g4xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32g474xx.o(RESET) refers to stm32g4xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32g474xx.o(RESET) refers to stm32g4xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32g474xx.o(RESET) refers to stm32g4xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32g474xx.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32g474xx.o(.text) refers to system_stm32g4xx.o(i.SystemInit) for SystemInit
    startup_stm32g474xx.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32g474xx.o(.text) refers to startup_stm32g474xx.o(HEAP) for Heap_Mem
    startup_stm32g474xx.o(.text) refers to startup_stm32g474xx.o(STACK) for Stack_Mem
    main.o(i.SystemClock_Config) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    main.o(i.SystemClock_Config) refers to stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling) for HAL_PWREx_ControlVoltageScaling
    main.o(i.SystemClock_Config) refers to stm32g4xx_hal_rcc.o(i.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(i.SystemClock_Config) refers to stm32g4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(i.main) refers to stm32g4xx_hal.o(i.HAL_Init) for HAL_Init
    main.o(i.main) refers to main.o(i.SystemClock_Config) for SystemClock_Config
    main.o(i.main) refers to gpio.o(i.MX_GPIO_Init) for MX_GPIO_Init
    main.o(i.main) refers to i2c.o(i.MX_I2C1_Init) for MX_I2C1_Init
    main.o(i.main) refers to oled_app.o(i.OLED_App_Init) for OLED_App_Init
    main.o(i.main) refers to oled_app.o(i.OLED_App_ShowHello) for OLED_App_ShowHello
    main.o(i.main) refers to scheduler.o(i.scheduler_init) for scheduler_init
    main.o(i.main) refers to scheduler.o(i.scheduler_run) for scheduler_run
    gpio.o(i.MX_GPIO_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    gpio.o(i.MX_GPIO_Init) refers to stm32g4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    i2c.o(i.HAL_I2C_MspDeInit) refers to stm32g4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    i2c.o(i.HAL_I2C_MspInit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    i2c.o(i.HAL_I2C_MspInit) refers to stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) for HAL_RCCEx_PeriphCLKConfig
    i2c.o(i.HAL_I2C_MspInit) refers to main.o(i.Error_Handler) for Error_Handler
    i2c.o(i.HAL_I2C_MspInit) refers to stm32g4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    i2c.o(i.MX_I2C1_Init) refers to stm32g4xx_hal_i2c.o(i.HAL_I2C_Init) for HAL_I2C_Init
    i2c.o(i.MX_I2C1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    i2c.o(i.MX_I2C1_Init) refers to stm32g4xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigAnalogFilter) for HAL_I2CEx_ConfigAnalogFilter
    i2c.o(i.MX_I2C1_Init) refers to stm32g4xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigDigitalFilter) for HAL_I2CEx_ConfigDigitalFilter
    i2c.o(i.MX_I2C1_Init) refers to i2c.o(.bss) for .bss
    stm32g4xx_it.o(i.SysTick_Handler) refers to stm32g4xx_hal.o(i.HAL_IncTick) for HAL_IncTick
    stm32g4xx_hal_msp.o(i.HAL_MspInit) refers to stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableUCPDDeadBattery) for HAL_PWREx_DisableUCPDDeadBattery
    stm32g4xx_hal_i2c.o(i.HAL_I2C_DeInit) refers to i2c.o(i.HAL_I2C_MspDeInit) for HAL_I2C_MspDeInit
    stm32g4xx_hal_i2c.o(i.HAL_I2C_DisableListen_IT) refers to stm32g4xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32g4xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler) refers to stm32g4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32g4xx_hal_i2c.o(i.HAL_I2C_EnableListen_IT) refers to stm32g4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32g4xx_hal_i2c.o(i.HAL_I2C_EnableListen_IT) refers to stm32g4xx_hal_i2c.o(i.I2C_Slave_ISR_IT) for I2C_Slave_ISR_IT
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Init) refers to i2c.o(i.HAL_I2C_MspInit) for HAL_I2C_MspInit
    stm32g4xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady) refers to stm32g4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT) refers to stm32g4xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT) refers to stm32g4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT) refers to stm32g4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32g4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32g4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32g4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32g4xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32g4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32g4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32g4xx_hal_i2c.o(i.I2C_Master_ISR_DMA) for I2C_Master_ISR_DMA
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32g4xx_hal_i2c.o(i.I2C_DMAMasterReceiveCplt) for I2C_DMAMasterReceiveCplt
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32g4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32g4xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT) refers to stm32g4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT) refers to stm32g4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT) refers to stm32g4xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32g4xx_hal_i2c.o(i.I2C_ConvertOtherXferOptions) for I2C_ConvertOtherXferOptions
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32g4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32g4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32g4xx_hal_i2c.o(i.I2C_Master_ISR_DMA) for I2C_Master_ISR_DMA
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32g4xx_hal_i2c.o(i.I2C_DMAMasterReceiveCplt) for I2C_DMAMasterReceiveCplt
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32g4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32g4xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT) refers to stm32g4xx_hal_i2c.o(i.I2C_ConvertOtherXferOptions) for I2C_ConvertOtherXferOptions
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT) refers to stm32g4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT) refers to stm32g4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT) refers to stm32g4xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32g4xx_hal_i2c.o(i.I2C_ConvertOtherXferOptions) for I2C_ConvertOtherXferOptions
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32g4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32g4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32g4xx_hal_i2c.o(i.I2C_Master_ISR_DMA) for I2C_Master_ISR_DMA
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32g4xx_hal_i2c.o(i.I2C_DMAMasterTransmitCplt) for I2C_DMAMasterTransmitCplt
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32g4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32g4xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT) refers to stm32g4xx_hal_i2c.o(i.I2C_ConvertOtherXferOptions) for I2C_ConvertOtherXferOptions
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT) refers to stm32g4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT) refers to stm32g4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT) refers to stm32g4xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32g4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32g4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32g4xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout) for I2C_WaitOnTXISFlagUntilTimeout
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32g4xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32g4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32g4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32g4xx_hal_i2c.o(i.I2C_Master_ISR_DMA) for I2C_Master_ISR_DMA
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32g4xx_hal_i2c.o(i.I2C_DMAMasterTransmitCplt) for I2C_DMAMasterTransmitCplt
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32g4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32g4xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT) refers to stm32g4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT) refers to stm32g4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT) refers to stm32g4xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32g4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32g4xx_hal_i2c.o(i.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32g4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32g4xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32g4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32g4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32g4xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) for I2C_Mem_ISR_DMA
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32g4xx_hal_i2c.o(i.I2C_DMAMasterReceiveCplt) for I2C_DMAMasterReceiveCplt
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32g4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT) refers to stm32g4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT) refers to stm32g4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT) refers to stm32g4xx_hal_i2c.o(i.I2C_Mem_ISR_IT) for I2C_Mem_ISR_IT
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32g4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32g4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32g4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32g4xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout) for I2C_WaitOnTXISFlagUntilTimeout
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32g4xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32g4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32g4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32g4xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) for I2C_Mem_ISR_DMA
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32g4xx_hal_i2c.o(i.I2C_DMAMasterTransmitCplt) for I2C_DMAMasterTransmitCplt
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32g4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT) refers to stm32g4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT) refers to stm32g4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT) refers to stm32g4xx_hal_i2c.o(i.I2C_Mem_ISR_IT) for I2C_Mem_ISR_IT
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32g4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32g4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32g4xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32g4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32g4xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) for I2C_Slave_ISR_DMA
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32g4xx_hal_i2c.o(i.I2C_DMASlaveReceiveCplt) for I2C_DMASlaveReceiveCplt
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32g4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_IT) refers to stm32g4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_IT) refers to stm32g4xx_hal_i2c.o(i.I2C_Slave_ISR_IT) for I2C_Slave_ISR_IT
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32g4xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32g4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32g4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32g4xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) for I2C_Slave_ISR_DMA
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32g4xx_hal_i2c.o(i.I2C_DMASlaveReceiveCplt) for I2C_DMASlaveReceiveCplt
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32g4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_IT) refers to stm32g4xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_IT) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_IT) refers to stm32g4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_IT) refers to stm32g4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_IT) refers to stm32g4xx_hal_i2c.o(i.I2C_Slave_ISR_IT) for I2C_Slave_ISR_IT
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32g4xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32g4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32g4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32g4xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) for I2C_Slave_ISR_DMA
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32g4xx_hal_i2c.o(i.I2C_DMASlaveTransmitCplt) for I2C_DMASlaveTransmitCplt
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32g4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_IT) refers to stm32g4xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_IT) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_IT) refers to stm32g4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_IT) refers to stm32g4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_IT) refers to stm32g4xx_hal_i2c.o(i.I2C_Slave_ISR_IT) for I2C_Slave_ISR_IT
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32g4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32g4xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32g4xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout) for I2C_WaitOnTXISFlagUntilTimeout
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32g4xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32g4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32g4xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) for I2C_Slave_ISR_DMA
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32g4xx_hal_i2c.o(i.I2C_DMASlaveTransmitCplt) for I2C_DMASlaveTransmitCplt
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32g4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_IT) refers to stm32g4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32g4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_IT) refers to stm32g4xx_hal_i2c.o(i.I2C_Slave_ISR_IT) for I2C_Slave_ISR_IT
    stm32g4xx_hal_i2c.o(i.I2C_DMAAbort) refers to stm32g4xx_hal_i2c.o(i.I2C_TreatErrorCallback) for I2C_TreatErrorCallback
    stm32g4xx_hal_i2c.o(i.I2C_DMAError) refers to stm32g4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32g4xx_hal_i2c.o(i.I2C_DMAMasterReceiveCplt) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g4xx_hal_i2c.o(i.I2C_DMAMasterReceiveCplt) refers to stm32g4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32g4xx_hal_i2c.o(i.I2C_DMAMasterReceiveCplt) refers to stm32g4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32g4xx_hal_i2c.o(i.I2C_DMAMasterTransmitCplt) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32g4xx_hal_i2c.o(i.I2C_DMAMasterTransmitCplt) refers to stm32g4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32g4xx_hal_i2c.o(i.I2C_DMAMasterTransmitCplt) refers to stm32g4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32g4xx_hal_i2c.o(i.I2C_DMASlaveReceiveCplt) refers to stm32g4xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt) for I2C_ITSlaveSeqCplt
    stm32g4xx_hal_i2c.o(i.I2C_DMASlaveTransmitCplt) refers to stm32g4xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt) for I2C_ITSlaveSeqCplt
    stm32g4xx_hal_i2c.o(i.I2C_Enable_IRQ) refers to stm32g4xx_hal_i2c.o(i.I2C_Master_ISR_DMA) for I2C_Master_ISR_DMA
    stm32g4xx_hal_i2c.o(i.I2C_Enable_IRQ) refers to stm32g4xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) for I2C_Slave_ISR_DMA
    stm32g4xx_hal_i2c.o(i.I2C_Enable_IRQ) refers to stm32g4xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) for I2C_Mem_ISR_DMA
    stm32g4xx_hal_i2c.o(i.I2C_ITAddrCplt) refers to stm32g4xx_hal_i2c.o(i.HAL_I2C_AddrCallback) for HAL_I2C_AddrCallback
    stm32g4xx_hal_i2c.o(i.I2C_ITAddrCplt) refers to stm32g4xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32g4xx_hal_i2c.o(i.I2C_ITError) refers to stm32g4xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32g4xx_hal_i2c.o(i.I2C_ITError) refers to stm32g4xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32g4xx_hal_i2c.o(i.I2C_ITError) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_GetState) for HAL_DMA_GetState
    stm32g4xx_hal_i2c.o(i.I2C_ITError) refers to stm32g4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32g4xx_hal_i2c.o(i.I2C_ITError) refers to stm32g4xx_hal_i2c.o(i.I2C_TreatErrorCallback) for I2C_TreatErrorCallback
    stm32g4xx_hal_i2c.o(i.I2C_ITError) refers to stm32g4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32g4xx_hal_i2c.o(i.I2C_ITError) refers to stm32g4xx_hal_i2c.o(i.I2C_Slave_ISR_IT) for I2C_Slave_ISR_IT
    stm32g4xx_hal_i2c.o(i.I2C_ITListenCplt) refers to stm32g4xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32g4xx_hal_i2c.o(i.I2C_ITListenCplt) refers to stm32g4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32g4xx_hal_i2c.o(i.I2C_ITMasterCplt) refers to stm32g4xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32g4xx_hal_i2c.o(i.I2C_ITMasterCplt) refers to stm32g4xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32g4xx_hal_i2c.o(i.I2C_ITMasterCplt) refers to stm32g4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32g4xx_hal_i2c.o(i.I2C_ITMasterCplt) refers to stm32g4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32g4xx_hal_i2c.o(i.I2C_ITMasterCplt) refers to stm32g4xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32g4xx_hal_i2c.o(i.I2C_ITMasterCplt) refers to stm32g4xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32g4xx_hal_i2c.o(i.I2C_ITMasterCplt) refers to stm32g4xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32g4xx_hal_i2c.o(i.I2C_ITMasterSeqCplt) refers to stm32g4xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32g4xx_hal_i2c.o(i.I2C_ITMasterSeqCplt) refers to stm32g4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32g4xx_hal_i2c.o(i.I2C_ITMasterSeqCplt) refers to stm32g4xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32g4xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32g4xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32g4xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32g4xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32g4xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32g4xx_hal_i2c.o(i.I2C_ITListenCplt) for I2C_ITListenCplt
    stm32g4xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32g4xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt) for I2C_ITSlaveSeqCplt
    stm32g4xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32g4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32g4xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32g4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32g4xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32g4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32g4xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32g4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32g4xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt) refers to stm32g4xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32g4xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt) refers to stm32g4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32g4xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt) refers to stm32g4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32g4xx_hal_i2c.o(i.I2C_IsErrorOccurred) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_i2c.o(i.I2C_IsErrorOccurred) refers to stm32g4xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32g4xx_hal_i2c.o(i.I2C_Master_ISR_DMA) refers to stm32g4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32g4xx_hal_i2c.o(i.I2C_Master_ISR_DMA) refers to stm32g4xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32g4xx_hal_i2c.o(i.I2C_Master_ISR_DMA) refers to stm32g4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32g4xx_hal_i2c.o(i.I2C_Master_ISR_DMA) refers to stm32g4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32g4xx_hal_i2c.o(i.I2C_Master_ISR_DMA) refers to stm32g4xx_hal_i2c.o(i.I2C_ITMasterSeqCplt) for I2C_ITMasterSeqCplt
    stm32g4xx_hal_i2c.o(i.I2C_Master_ISR_DMA) refers to stm32g4xx_hal_i2c.o(i.I2C_ITMasterCplt) for I2C_ITMasterCplt
    stm32g4xx_hal_i2c.o(i.I2C_Master_ISR_IT) refers to stm32g4xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32g4xx_hal_i2c.o(i.I2C_Master_ISR_IT) refers to stm32g4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32g4xx_hal_i2c.o(i.I2C_Master_ISR_IT) refers to stm32g4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32g4xx_hal_i2c.o(i.I2C_Master_ISR_IT) refers to stm32g4xx_hal_i2c.o(i.I2C_ITMasterCplt) for I2C_ITMasterCplt
    stm32g4xx_hal_i2c.o(i.I2C_Master_ISR_IT) refers to stm32g4xx_hal_i2c.o(i.I2C_ITMasterSeqCplt) for I2C_ITMasterSeqCplt
    stm32g4xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) refers to stm32g4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32g4xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) refers to stm32g4xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32g4xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) refers to stm32g4xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32g4xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) refers to stm32g4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32g4xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) refers to stm32g4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32g4xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) refers to stm32g4xx_hal_i2c.o(i.I2C_ITMasterCplt) for I2C_ITMasterCplt
    stm32g4xx_hal_i2c.o(i.I2C_Mem_ISR_IT) refers to stm32g4xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32g4xx_hal_i2c.o(i.I2C_Mem_ISR_IT) refers to stm32g4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32g4xx_hal_i2c.o(i.I2C_Mem_ISR_IT) refers to stm32g4xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32g4xx_hal_i2c.o(i.I2C_Mem_ISR_IT) refers to stm32g4xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32g4xx_hal_i2c.o(i.I2C_Mem_ISR_IT) refers to stm32g4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32g4xx_hal_i2c.o(i.I2C_Mem_ISR_IT) refers to stm32g4xx_hal_i2c.o(i.I2C_ITMasterCplt) for I2C_ITMasterCplt
    stm32g4xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32g4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32g4xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32g4xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout) for I2C_WaitOnTXISFlagUntilTimeout
    stm32g4xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32g4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32g4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32g4xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32g4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32g4xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout) for I2C_WaitOnTXISFlagUntilTimeout
    stm32g4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32g4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32g4xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) refers to stm32g4xx_hal_i2c.o(i.I2C_ITSlaveCplt) for I2C_ITSlaveCplt
    stm32g4xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) refers to stm32g4xx_hal_i2c.o(i.I2C_ITListenCplt) for I2C_ITListenCplt
    stm32g4xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) refers to stm32g4xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32g4xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) refers to stm32g4xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt) for I2C_ITSlaveSeqCplt
    stm32g4xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) refers to stm32g4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32g4xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) refers to stm32g4xx_hal_i2c.o(i.I2C_ITAddrCplt) for I2C_ITAddrCplt
    stm32g4xx_hal_i2c.o(i.I2C_Slave_ISR_IT) refers to stm32g4xx_hal_i2c.o(i.I2C_ITSlaveCplt) for I2C_ITSlaveCplt
    stm32g4xx_hal_i2c.o(i.I2C_Slave_ISR_IT) refers to stm32g4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32g4xx_hal_i2c.o(i.I2C_Slave_ISR_IT) refers to stm32g4xx_hal_i2c.o(i.I2C_ITListenCplt) for I2C_ITListenCplt
    stm32g4xx_hal_i2c.o(i.I2C_Slave_ISR_IT) refers to stm32g4xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32g4xx_hal_i2c.o(i.I2C_Slave_ISR_IT) refers to stm32g4xx_hal_i2c.o(i.I2C_ITAddrCplt) for I2C_ITAddrCplt
    stm32g4xx_hal_i2c.o(i.I2C_Slave_ISR_IT) refers to stm32g4xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt) for I2C_ITSlaveSeqCplt
    stm32g4xx_hal_i2c.o(i.I2C_TreatErrorCallback) refers to stm32g4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32g4xx_hal_i2c.o(i.I2C_TreatErrorCallback) refers to stm32g4xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32g4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) refers to stm32g4xx_hal_i2c.o(i.I2C_IsErrorOccurred) for I2C_IsErrorOccurred
    stm32g4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) refers to stm32g4xx_hal_i2c.o(i.I2C_IsErrorOccurred) for I2C_IsErrorOccurred
    stm32g4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) refers to stm32g4xx_hal_i2c.o(i.I2C_IsErrorOccurred) for I2C_IsErrorOccurred
    stm32g4xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout) refers to stm32g4xx_hal_i2c.o(i.I2C_IsErrorOccurred) for I2C_IsErrorOccurred
    stm32g4xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal.o(i.HAL_DeInit) refers to stm32g4xx_hal.o(i.HAL_MspDeInit) for HAL_MspDeInit
    stm32g4xx_hal.o(i.HAL_Delay) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal.o(i.HAL_Delay) refers to stm32g4xx_hal.o(.data) for .data
    stm32g4xx_hal.o(i.HAL_GetTick) refers to stm32g4xx_hal.o(.data) for .data
    stm32g4xx_hal.o(i.HAL_GetTickFreq) refers to stm32g4xx_hal.o(.data) for .data
    stm32g4xx_hal.o(i.HAL_GetTickPrio) refers to stm32g4xx_hal.o(.data) for .data
    stm32g4xx_hal.o(i.HAL_IncTick) refers to stm32g4xx_hal.o(.data) for .data
    stm32g4xx_hal.o(i.HAL_Init) refers to stm32g4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32g4xx_hal.o(i.HAL_Init) refers to stm32g4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32g4xx_hal.o(i.HAL_Init) refers to stm32g4xx_hal_msp.o(i.HAL_MspInit) for HAL_MspInit
    stm32g4xx_hal.o(i.HAL_InitTick) refers to stm32g4xx_hal_cortex.o(i.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32g4xx_hal.o(i.HAL_InitTick) refers to stm32g4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32g4xx_hal.o(i.HAL_InitTick) refers to stm32g4xx_hal.o(.data) for .data
    stm32g4xx_hal.o(i.HAL_InitTick) refers to system_stm32g4xx.o(.data) for SystemCoreClock
    stm32g4xx_hal.o(i.HAL_SYSCFG_EnableVREFBUF) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal.o(i.HAL_SetTickFreq) refers to stm32g4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32g4xx_hal.o(i.HAL_SetTickFreq) refers to stm32g4xx_hal.o(.data) for .data
    stm32g4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32g4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32g4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32g4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32g4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32g4xx.o(.constdata) for AHBPrescTable
    stm32g4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32g4xx.o(.data) for SystemCoreClock
    stm32g4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32g4xx_hal.o(.data) for uwTickPrio
    stm32g4xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32g4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32g4xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to system_stm32g4xx.o(.data) for SystemCoreClock
    stm32g4xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32g4xx_hal.o(.data) for uwTickPrio
    stm32g4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to system_stm32g4xx.o(.data) for SystemCoreClock
    stm32g4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32g4xx.o(.data) for SystemCoreClock
    stm32g4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32g4xx.o(.constdata) for APBPrescTable
    stm32g4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32g4xx.o(.data) for SystemCoreClock
    stm32g4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32g4xx.o(.constdata) for APBPrescTable
    stm32g4xx_hal_rcc.o(i.HAL_RCC_MCOConfig) refers to stm32g4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32g4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler) refers to stm32g4xx_hal_rcc.o(i.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32g4xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32g4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32g4xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32g4xx_hal.o(.data) for uwTickPrio
    stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSWaitSynchronization) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_IRQHandler) refers to stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_SyncOkCallback) for HAL_RCCEx_CRS_SyncOkCallback
    stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_IRQHandler) refers to stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_SyncWarnCallback) for HAL_RCCEx_CRS_SyncWarnCallback
    stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_IRQHandler) refers to stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_ExpectedSyncCallback) for HAL_RCCEx_CRS_ExpectedSyncCallback
    stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_IRQHandler) refers to stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_ErrorCallback) for HAL_RCCEx_CRS_ErrorCallback
    stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisableLSCO) refers to stm32g4xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess) for HAL_PWR_EnableBkUpAccess
    stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisableLSCO) refers to stm32g4xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess) for HAL_PWR_DisableBkUpAccess
    stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableLSCO) refers to stm32g4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableLSCO) refers to stm32g4xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess) for HAL_PWR_EnableBkUpAccess
    stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableLSCO) refers to stm32g4xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess) for HAL_PWR_DisableBkUpAccess
    stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32g4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32g4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32g4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_LSECSS_IRQHandler) refers to stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_LSECSS_Callback) for HAL_RCCEx_LSECSS_Callback
    stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32g4xx_hal_flash.o(.data) for .data
    stm32g4xx_hal_flash.o(i.HAL_FLASH_GetError) refers to stm32g4xx_hal_flash.o(.data) for .data
    stm32g4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32g4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32g4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32g4xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32g4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32g4xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32g4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32g4xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32g4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32g4xx_hal_flash.o(.data) for .data
    stm32g4xx_hal_flash.o(i.HAL_FLASH_OB_Launch) refers to stm32g4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32g4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32g4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32g4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32g4xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32g4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32g4xx_hal_flash.o(i.FLASH_Program_Fast) for FLASH_Program_Fast
    stm32g4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32g4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32g4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32g4xx_hal_flash.o(.data) for .data
    stm32g4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32g4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32g4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32g4xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32g4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32g4xx_hal_flash.o(i.FLASH_Program_Fast) for FLASH_Program_Fast
    stm32g4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32g4xx_hal_flash.o(.data) for .data
    stm32g4xx_hal_flash_ex.o(i.FLASH_FlushCaches) refers to stm32g4xx_hal_flash.o(.data) for pFlash
    stm32g4xx_hal_flash_ex.o(i.FLASH_OB_PCROPConfig) refers to stm32g4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32g4xx_hal_flash_ex.o(i.FLASH_OB_UserConfig) refers to stm32g4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32g4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32g4xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32g4xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32g4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32g4xx_hal_flash.o(.data) for pFlash
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32g4xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32g4xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32g4xx_hal_flash.o(.data) for pFlash
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32g4xx_hal_flash_ex.o(i.FLASH_OB_GetPCROP) for FLASH_OB_GetPCROP
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32g4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32g4xx_hal_flash_ex.o(i.FLASH_OB_UserConfig) for FLASH_OB_UserConfig
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32g4xx_hal_flash_ex.o(i.FLASH_OB_PCROPConfig) for FLASH_OB_PCROPConfig
    stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32g4xx_hal_flash.o(.data) for pFlash
    stm32g4xx_hal_flash_ramfunc.o(i.HAL_FLASHEx_OB_DBankConfig) refers to stm32g4xx_hal_flash.o(.data) for pFlash
    stm32g4xx_hal_flash_ramfunc.o(i.HAL_FLASHEx_OB_DBankConfig) refers to system_stm32g4xx.o(.data) for SystemCoreClock
    stm32g4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) refers to stm32g4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32g4xx_hal_dma.o(i.HAL_DMA_DeInit) refers to stm32g4xx_hal_dma.o(i.DMA_CalcDMAMUXChannelBaseAndMask) for DMA_CalcDMAMUXChannelBaseAndMask
    stm32g4xx_hal_dma.o(i.HAL_DMA_DeInit) refers to stm32g4xx_hal_dma.o(i.DMA_CalcDMAMUXRequestGenBaseAndMask) for DMA_CalcDMAMUXRequestGenBaseAndMask
    stm32g4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32g4xx_hal_dma.o(i.DMA_CalcDMAMUXChannelBaseAndMask) for DMA_CalcDMAMUXChannelBaseAndMask
    stm32g4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32g4xx_hal_dma.o(i.DMA_CalcDMAMUXRequestGenBaseAndMask) for DMA_CalcDMAMUXRequestGenBaseAndMask
    stm32g4xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32g4xx_hal_dma.o(i.HAL_DMA_Start) refers to stm32g4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32g4xx_hal_dma.o(i.HAL_DMA_Start_IT) refers to stm32g4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32g4xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode) refers to stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableLowPowerRunMode) for HAL_PWREx_EnableLowPowerRunMode
    stm32g4xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode) refers to stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableLowPowerRunMode) for HAL_PWREx_DisableLowPowerRunMode
    stm32g4xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode) refers to stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnterSTOP0Mode) for HAL_PWREx_EnterSTOP0Mode
    stm32g4xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode) refers to stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnterSTOP1Mode) for HAL_PWREx_EnterSTOP1Mode
    stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling) refers to system_stm32g4xx.o(.data) for SystemCoreClock
    stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableLowPowerRunMode) refers to system_stm32g4xx.o(.data) for SystemCoreClock
    stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_PVD_PVM_IRQHandler) refers to stm32g4xx_hal_pwr.o(i.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_PVD_PVM_IRQHandler) refers to stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_PVM1Callback) for HAL_PWREx_PVM1Callback
    stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_PVD_PVM_IRQHandler) refers to stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_PVM2Callback) for HAL_PWREx_PVM2Callback
    stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_PVD_PVM_IRQHandler) refers to stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_PVM3Callback) for HAL_PWREx_PVM3Callback
    stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_PVD_PVM_IRQHandler) refers to stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_PVM4Callback) for HAL_PWREx_PVM4Callback
    stm32g4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) refers to stm32g4xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32g4xx_hal_cortex.o(i.HAL_SYSTICK_Config) refers to stm32g4xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32g4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler) refers to stm32g4xx_hal_cortex.o(i.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    system_stm32g4xx.o(i.SystemCoreClockUpdate) refers to system_stm32g4xx.o(.data) for .data
    system_stm32g4xx.o(i.SystemCoreClockUpdate) refers to system_stm32g4xx.o(.constdata) for .constdata
    scheduler.o(i.scheduler_init) refers to scheduler.o(.data) for .data
    scheduler.o(i.scheduler_run) refers to stm32g4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    scheduler.o(i.scheduler_run) refers to scheduler.o(.data) for .data
    scheduler.o(.data) refers to key_app.o(i.key_task) for key_task
    scheduler.o(.data) refers to oled_app.o(i.OLED_task) for OLED_task
    key_app.o(i.key_read) refers to stm32g4xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    key_app.o(i.key_task) refers to key_app.o(i.key_read) for key_read
    key_app.o(i.key_task) refers to key_app.o(.data) for .data
    oled_app.o(i.OLED_App_Init) refers to oled.o(i.OLED_Init) for OLED_Init
    oled_app.o(i.OLED_App_Init) refers to oled.o(i.OLED_Clear) for OLED_Clear
    oled_app.o(i.OLED_App_ShowHello) refers to oled.o(i.OLED_ShowStr) for OLED_ShowStr
    oled.o(i.OLED_Allfill) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_Allfill) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_Clear) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_Clear) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_Display_Off) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_Display_On) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_Init) refers to stm32g4xx_hal.o(i.HAL_Delay) for HAL_Delay
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Clear) for OLED_Clear
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Set_Position) for OLED_Set_Position
    oled.o(i.OLED_Init) refers to oled.o(.data) for .data
    oled.o(i.OLED_Set_Position) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_Set_Position) for OLED_Set_Position
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_ShowChar) refers to oled.o(.constdata) for .constdata
    oled.o(i.OLED_ShowFloat) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowHanzi) refers to oled.o(i.OLED_Set_Position) for OLED_Set_Position
    oled.o(i.OLED_ShowHanzi) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_ShowHanzi) refers to oled.o(.constdata) for .constdata
    oled.o(i.OLED_ShowHzbig) refers to oled.o(i.OLED_Set_Position) for OLED_Set_Position
    oled.o(i.OLED_ShowHzbig) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_ShowHzbig) refers to oled.o(.constdata) for .constdata
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowPic) refers to oled.o(i.OLED_Set_Position) for OLED_Set_Position
    oled.o(i.OLED_ShowPic) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_ShowStr) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_Write_cmd) refers to stm32g4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    oled.o(i.OLED_Write_cmd) refers to i2c.o(.bss) for hi2c1
    oled.o(i.OLED_Write_data) refers to stm32g4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    oled.o(i.OLED_Write_data) refers to i2c.o(.bss) for hi2c1
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32g474xx.o(.text) for __user_initial_stackheap
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000001) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    libinit2.o(.ARM.Collect$$libinit$$00000001) refers to fpinit.o(x$fpl$fpinit) for _fp_init
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000007) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000010) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_user_alloc_1
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing gpio.o(.rev16_text), (4 bytes).
    Removing gpio.o(.revsh_text), (4 bytes).
    Removing gpio.o(.rrx_text), (6 bytes).
    Removing i2c.o(.rev16_text), (4 bytes).
    Removing i2c.o(.revsh_text), (4 bytes).
    Removing i2c.o(.rrx_text), (6 bytes).
    Removing i2c.o(i.HAL_I2C_MspDeInit), (60 bytes).
    Removing stm32g4xx_it.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_it.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_it.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_msp.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_msp.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_msp.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_i2c.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_i2c.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_i2c.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback), (2 bytes).
    Removing stm32g4xx_hal_i2c.o(i.HAL_I2C_AddrCallback), (2 bytes).
    Removing stm32g4xx_hal_i2c.o(i.HAL_I2C_DeInit), (50 bytes).
    Removing stm32g4xx_hal_i2c.o(i.HAL_I2C_DisableListen_IT), (56 bytes).
    Removing stm32g4xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler), (104 bytes).
    Removing stm32g4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler), (16 bytes).
    Removing stm32g4xx_hal_i2c.o(i.HAL_I2C_EnableListen_IT), (40 bytes).
    Removing stm32g4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback), (2 bytes).
    Removing stm32g4xx_hal_i2c.o(i.HAL_I2C_GetError), (4 bytes).
    Removing stm32g4xx_hal_i2c.o(i.HAL_I2C_GetMode), (6 bytes).
    Removing stm32g4xx_hal_i2c.o(i.HAL_I2C_GetState), (6 bytes).
    Removing stm32g4xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady), (260 bytes).
    Removing stm32g4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback), (2 bytes).
    Removing stm32g4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback), (2 bytes).
    Removing stm32g4xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback), (2 bytes).
    Removing stm32g4xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT), (128 bytes).
    Removing stm32g4xx_hal_i2c.o(i.HAL_I2C_Master_Receive), (292 bytes).
    Removing stm32g4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA), (304 bytes).
    Removing stm32g4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT), (136 bytes).
    Removing stm32g4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA), (336 bytes).
    Removing stm32g4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT), (164 bytes).
    Removing stm32g4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA), (400 bytes).
    Removing stm32g4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT), (224 bytes).
    Removing stm32g4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit), (332 bytes).
    Removing stm32g4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA), (340 bytes).
    Removing stm32g4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT), (164 bytes).
    Removing stm32g4xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback), (2 bytes).
    Removing stm32g4xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback), (2 bytes).
    Removing stm32g4xx_hal_i2c.o(i.HAL_I2C_Mem_Read), (356 bytes).
    Removing stm32g4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA), (268 bytes).
    Removing stm32g4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT), (152 bytes).
    Removing stm32g4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA), (268 bytes).
    Removing stm32g4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT), (156 bytes).
    Removing stm32g4xx_hal_i2c.o(i.HAL_I2C_MspDeInit), (2 bytes).
    Removing stm32g4xx_hal_i2c.o(i.HAL_I2C_MspInit), (2 bytes).
    Removing stm32g4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback), (2 bytes).
    Removing stm32g4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback), (2 bytes).
    Removing stm32g4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive), (314 bytes).
    Removing stm32g4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA), (224 bytes).
    Removing stm32g4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_IT), (96 bytes).
    Removing stm32g4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA), (372 bytes).
    Removing stm32g4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_IT), (208 bytes).
    Removing stm32g4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA), (372 bytes).
    Removing stm32g4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_IT), (208 bytes).
    Removing stm32g4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit), (382 bytes).
    Removing stm32g4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA), (288 bytes).
    Removing stm32g4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_IT), (128 bytes).
    Removing stm32g4xx_hal_i2c.o(i.I2C_ConvertOtherXferOptions), (26 bytes).
    Removing stm32g4xx_hal_i2c.o(i.I2C_DMAAbort), (20 bytes).
    Removing stm32g4xx_hal_i2c.o(i.I2C_DMAError), (18 bytes).
    Removing stm32g4xx_hal_i2c.o(i.I2C_DMAMasterReceiveCplt), (82 bytes).
    Removing stm32g4xx_hal_i2c.o(i.I2C_DMAMasterTransmitCplt), (82 bytes).
    Removing stm32g4xx_hal_i2c.o(i.I2C_DMASlaveReceiveCplt), (36 bytes).
    Removing stm32g4xx_hal_i2c.o(i.I2C_DMASlaveTransmitCplt), (30 bytes).
    Removing stm32g4xx_hal_i2c.o(i.I2C_Disable_IRQ), (94 bytes).
    Removing stm32g4xx_hal_i2c.o(i.I2C_Enable_IRQ), (136 bytes).
    Removing stm32g4xx_hal_i2c.o(i.I2C_ITAddrCplt), (140 bytes).
    Removing stm32g4xx_hal_i2c.o(i.I2C_ITError), (284 bytes).
    Removing stm32g4xx_hal_i2c.o(i.I2C_ITListenCplt), (100 bytes).
    Removing stm32g4xx_hal_i2c.o(i.I2C_ITMasterCplt), (240 bytes).
    Removing stm32g4xx_hal_i2c.o(i.I2C_ITMasterSeqCplt), (76 bytes).
    Removing stm32g4xx_hal_i2c.o(i.I2C_ITSlaveCplt), (444 bytes).
    Removing stm32g4xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt), (114 bytes).
    Removing stm32g4xx_hal_i2c.o(i.I2C_Master_ISR_DMA), (272 bytes).
    Removing stm32g4xx_hal_i2c.o(i.I2C_Master_ISR_IT), (304 bytes).
    Removing stm32g4xx_hal_i2c.o(i.I2C_Mem_ISR_DMA), (312 bytes).
    Removing stm32g4xx_hal_i2c.o(i.I2C_Mem_ISR_IT), (328 bytes).
    Removing stm32g4xx_hal_i2c.o(i.I2C_RequestMemoryRead), (100 bytes).
    Removing stm32g4xx_hal_i2c.o(i.I2C_Slave_ISR_DMA), (248 bytes).
    Removing stm32g4xx_hal_i2c.o(i.I2C_Slave_ISR_IT), (280 bytes).
    Removing stm32g4xx_hal_i2c.o(i.I2C_TreatErrorCallback), (42 bytes).
    Removing stm32g4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout), (168 bytes).
    Removing stm32g4xx_hal_i2c_ex.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_i2c_ex.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_i2c_ex.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_i2c_ex.o(i.HAL_I2CEx_DisableFastModePlus), (40 bytes).
    Removing stm32g4xx_hal_i2c_ex.o(i.HAL_I2CEx_DisableWakeUp), (78 bytes).
    Removing stm32g4xx_hal_i2c_ex.o(i.HAL_I2CEx_EnableFastModePlus), (40 bytes).
    Removing stm32g4xx_hal_i2c_ex.o(i.HAL_I2CEx_EnableWakeUp), (78 bytes).
    Removing stm32g4xx_hal.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal.o(i.HAL_DBGMCU_DisableDBGSleepMode), (16 bytes).
    Removing stm32g4xx_hal.o(i.HAL_DBGMCU_DisableDBGStandbyMode), (16 bytes).
    Removing stm32g4xx_hal.o(i.HAL_DBGMCU_DisableDBGStopMode), (16 bytes).
    Removing stm32g4xx_hal.o(i.HAL_DBGMCU_EnableDBGSleepMode), (16 bytes).
    Removing stm32g4xx_hal.o(i.HAL_DBGMCU_EnableDBGStandbyMode), (16 bytes).
    Removing stm32g4xx_hal.o(i.HAL_DBGMCU_EnableDBGStopMode), (16 bytes).
    Removing stm32g4xx_hal.o(i.HAL_DeInit), (44 bytes).
    Removing stm32g4xx_hal.o(i.HAL_GetDEVID), (16 bytes).
    Removing stm32g4xx_hal.o(i.HAL_GetHalVersion), (8 bytes).
    Removing stm32g4xx_hal.o(i.HAL_GetREVID), (12 bytes).
    Removing stm32g4xx_hal.o(i.HAL_GetTickFreq), (12 bytes).
    Removing stm32g4xx_hal.o(i.HAL_GetTickPrio), (12 bytes).
    Removing stm32g4xx_hal.o(i.HAL_GetUIDw0), (12 bytes).
    Removing stm32g4xx_hal.o(i.HAL_GetUIDw1), (12 bytes).
    Removing stm32g4xx_hal.o(i.HAL_GetUIDw2), (12 bytes).
    Removing stm32g4xx_hal.o(i.HAL_MspDeInit), (2 bytes).
    Removing stm32g4xx_hal.o(i.HAL_MspInit), (2 bytes).
    Removing stm32g4xx_hal.o(i.HAL_ResumeTick), (14 bytes).
    Removing stm32g4xx_hal.o(i.HAL_SYSCFG_CCMSRAMErase), (24 bytes).
    Removing stm32g4xx_hal.o(i.HAL_SYSCFG_CCMSRAM_WriteProtectionEnable), (16 bytes).
    Removing stm32g4xx_hal.o(i.HAL_SYSCFG_DisableIOSwitchBooster), (16 bytes).
    Removing stm32g4xx_hal.o(i.HAL_SYSCFG_DisableIOSwitchVDD), (16 bytes).
    Removing stm32g4xx_hal.o(i.HAL_SYSCFG_DisableMemorySwappingBank), (16 bytes).
    Removing stm32g4xx_hal.o(i.HAL_SYSCFG_DisableVREFBUF), (16 bytes).
    Removing stm32g4xx_hal.o(i.HAL_SYSCFG_EnableIOSwitchBooster), (16 bytes).
    Removing stm32g4xx_hal.o(i.HAL_SYSCFG_EnableIOSwitchVDD), (16 bytes).
    Removing stm32g4xx_hal.o(i.HAL_SYSCFG_EnableMemorySwappingBank), (16 bytes).
    Removing stm32g4xx_hal.o(i.HAL_SYSCFG_EnableVREFBUF), (48 bytes).
    Removing stm32g4xx_hal.o(i.HAL_SYSCFG_VREFBUF_HighImpedanceConfig), (20 bytes).
    Removing stm32g4xx_hal.o(i.HAL_SYSCFG_VREFBUF_TrimmingConfig), (20 bytes).
    Removing stm32g4xx_hal.o(i.HAL_SYSCFG_VREFBUF_VoltageScalingConfig), (20 bytes).
    Removing stm32g4xx_hal.o(i.HAL_SetTickFreq), (36 bytes).
    Removing stm32g4xx_hal.o(i.HAL_SuspendTick), (14 bytes).
    Removing stm32g4xx_hal_rcc.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_rcc.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_rcc.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_rcc.o(i.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32g4xx_hal_rcc.o(i.HAL_RCC_DeInit), (184 bytes).
    Removing stm32g4xx_hal_rcc.o(i.HAL_RCC_DisableLSECSS), (20 bytes).
    Removing stm32g4xx_hal_rcc.o(i.HAL_RCC_EnableCSS), (16 bytes).
    Removing stm32g4xx_hal_rcc.o(i.HAL_RCC_EnableLSECSS), (20 bytes).
    Removing stm32g4xx_hal_rcc.o(i.HAL_RCC_GetClockConfig), (64 bytes).
    Removing stm32g4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq), (12 bytes).
    Removing stm32g4xx_hal_rcc.o(i.HAL_RCC_GetOscConfig), (200 bytes).
    Removing stm32g4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq), (36 bytes).
    Removing stm32g4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq), (36 bytes).
    Removing stm32g4xx_hal_rcc.o(i.HAL_RCC_MCOConfig), (96 bytes).
    Removing stm32g4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler), (24 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSConfig), (76 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSGetSynchronizationInfo), (36 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSSoftwareSynchronizationGenerate), (16 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSWaitSynchronization), (132 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_ErrorCallback), (2 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_ExpectedSyncCallback), (2 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_IRQHandler), (108 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_SyncOkCallback), (2 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_SyncWarnCallback), (2 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisableLSCO), (88 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisableLSECSS), (28 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableLSCO), (140 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableLSECSS), (20 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableLSECSS_IT), (56 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKConfig), (180 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq), (1016 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_LSECSS_Callback), (2 bytes).
    Removing stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_LSECSS_IRQHandler), (24 bytes).
    Removing stm32g4xx_hal_flash.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_flash.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_flash.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_flash.o(i.FLASH_Program_DoubleWord), (28 bytes).
    Removing stm32g4xx_hal_flash.o(i.FLASH_Program_Fast), (44 bytes).
    Removing stm32g4xx_hal_flash.o(i.FLASH_WaitForLastOperation), (96 bytes).
    Removing stm32g4xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32g4xx_hal_flash.o(i.HAL_FLASH_GetError), (12 bytes).
    Removing stm32g4xx_hal_flash.o(i.HAL_FLASH_IRQHandler), (236 bytes).
    Removing stm32g4xx_hal_flash.o(i.HAL_FLASH_Lock), (28 bytes).
    Removing stm32g4xx_hal_flash.o(i.HAL_FLASH_OB_Launch), (24 bytes).
    Removing stm32g4xx_hal_flash.o(i.HAL_FLASH_OB_Lock), (28 bytes).
    Removing stm32g4xx_hal_flash.o(i.HAL_FLASH_OB_Unlock), (40 bytes).
    Removing stm32g4xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32g4xx_hal_flash.o(i.HAL_FLASH_Program), (164 bytes).
    Removing stm32g4xx_hal_flash.o(i.HAL_FLASH_Program_IT), (144 bytes).
    Removing stm32g4xx_hal_flash.o(i.HAL_FLASH_Unlock), (40 bytes).
    Removing stm32g4xx_hal_flash.o(.data), (32 bytes).
    Removing stm32g4xx_hal_flash_ex.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_flash_ex.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_flash_ex.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.FLASH_FlushCaches), (92 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.FLASH_MassErase), (56 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.FLASH_OB_GetPCROP), (208 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.FLASH_OB_PCROPConfig), (268 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.FLASH_OB_UserConfig), (284 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.FLASH_PageErase), (64 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_DisableDebugger), (16 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_EnableDebugger), (16 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_EnableSecMemProtection), (48 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase), (220 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT), (140 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig), (212 bytes).
    Removing stm32g4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram), (356 bytes).
    Removing stm32g4xx_hal_flash_ramfunc.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_flash_ramfunc.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_flash_ramfunc.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_flash_ramfunc.o(i.HAL_FLASHEx_DisableRunPowerDown), (36 bytes).
    Removing stm32g4xx_hal_flash_ramfunc.o(i.HAL_FLASHEx_EnableRunPowerDown), (36 bytes).
    Removing stm32g4xx_hal_flash_ramfunc.o(i.HAL_FLASHEx_OB_DBankConfig), (300 bytes).
    Removing stm32g4xx_hal_gpio.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_gpio.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_gpio.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_gpio.o(i.HAL_GPIO_DeInit), (336 bytes).
    Removing stm32g4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback), (2 bytes).
    Removing stm32g4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler), (24 bytes).
    Removing stm32g4xx_hal_gpio.o(i.HAL_GPIO_LockPin), (34 bytes).
    Removing stm32g4xx_hal_gpio.o(i.HAL_GPIO_TogglePin), (16 bytes).
    Removing stm32g4xx_hal_gpio.o(i.HAL_GPIO_WritePin), (10 bytes).
    Removing stm32g4xx_hal_exti.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_exti.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_exti.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_exti.o(i.HAL_EXTI_ClearConfigLine), (128 bytes).
    Removing stm32g4xx_hal_exti.o(i.HAL_EXTI_ClearPending), (28 bytes).
    Removing stm32g4xx_hal_exti.o(i.HAL_EXTI_GenerateSWI), (28 bytes).
    Removing stm32g4xx_hal_exti.o(i.HAL_EXTI_GetConfigLine), (160 bytes).
    Removing stm32g4xx_hal_exti.o(i.HAL_EXTI_GetHandle), (12 bytes).
    Removing stm32g4xx_hal_exti.o(i.HAL_EXTI_GetPending), (32 bytes).
    Removing stm32g4xx_hal_exti.o(i.HAL_EXTI_IRQHandler), (44 bytes).
    Removing stm32g4xx_hal_exti.o(i.HAL_EXTI_RegisterCallback), (16 bytes).
    Removing stm32g4xx_hal_exti.o(i.HAL_EXTI_SetConfigLine), (176 bytes).
    Removing stm32g4xx_hal_dma.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_dma.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_dma.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_dma.o(i.DMA_CalcDMAMUXChannelBaseAndMask), (64 bytes).
    Removing stm32g4xx_hal_dma.o(i.DMA_CalcDMAMUXRequestGenBaseAndMask), (36 bytes).
    Removing stm32g4xx_hal_dma.o(i.DMA_SetConfig), (62 bytes).
    Removing stm32g4xx_hal_dma.o(i.HAL_DMA_Abort), (106 bytes).
    Removing stm32g4xx_hal_dma.o(i.HAL_DMA_Abort_IT), (120 bytes).
    Removing stm32g4xx_hal_dma.o(i.HAL_DMA_DeInit), (168 bytes).
    Removing stm32g4xx_hal_dma.o(i.HAL_DMA_GetError), (4 bytes).
    Removing stm32g4xx_hal_dma.o(i.HAL_DMA_GetState), (6 bytes).
    Removing stm32g4xx_hal_dma.o(i.HAL_DMA_IRQHandler), (186 bytes).
    Removing stm32g4xx_hal_dma.o(i.HAL_DMA_Init), (192 bytes).
    Removing stm32g4xx_hal_dma.o(i.HAL_DMA_PollForTransfer), (274 bytes).
    Removing stm32g4xx_hal_dma.o(i.HAL_DMA_RegisterCallback), (74 bytes).
    Removing stm32g4xx_hal_dma.o(i.HAL_DMA_Start), (80 bytes).
    Removing stm32g4xx_hal_dma.o(i.HAL_DMA_Start_IT), (140 bytes).
    Removing stm32g4xx_hal_dma.o(i.HAL_DMA_UnRegisterCallback), (82 bytes).
    Removing stm32g4xx_hal_dma_ex.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_dma_ex.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_dma_ex.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_dma_ex.o(i.HAL_DMAEx_ConfigMuxRequestGenerator), (76 bytes).
    Removing stm32g4xx_hal_dma_ex.o(i.HAL_DMAEx_ConfigMuxSync), (84 bytes).
    Removing stm32g4xx_hal_dma_ex.o(i.HAL_DMAEx_DisableMuxRequestGenerator), (26 bytes).
    Removing stm32g4xx_hal_dma_ex.o(i.HAL_DMAEx_EnableMuxRequestGenerator), (26 bytes).
    Removing stm32g4xx_hal_dma_ex.o(i.HAL_DMAEx_MUX_IRQHandler), (100 bytes).
    Removing stm32g4xx_hal_pwr.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_pwr.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_pwr.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_pwr.o(i.HAL_PWR_ConfigPVD), (128 bytes).
    Removing stm32g4xx_hal_pwr.o(i.HAL_PWR_DeInit), (24 bytes).
    Removing stm32g4xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess), (16 bytes).
    Removing stm32g4xx_hal_pwr.o(i.HAL_PWR_DisablePVD), (16 bytes).
    Removing stm32g4xx_hal_pwr.o(i.HAL_PWR_DisableSEVOnPend), (16 bytes).
    Removing stm32g4xx_hal_pwr.o(i.HAL_PWR_DisableSleepOnExit), (16 bytes).
    Removing stm32g4xx_hal_pwr.o(i.HAL_PWR_DisableWakeUpPin), (20 bytes).
    Removing stm32g4xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess), (16 bytes).
    Removing stm32g4xx_hal_pwr.o(i.HAL_PWR_EnablePVD), (16 bytes).
    Removing stm32g4xx_hal_pwr.o(i.HAL_PWR_EnableSEVOnPend), (16 bytes).
    Removing stm32g4xx_hal_pwr.o(i.HAL_PWR_EnableSleepOnExit), (16 bytes).
    Removing stm32g4xx_hal_pwr.o(i.HAL_PWR_EnableWakeUpPin), (28 bytes).
    Removing stm32g4xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode), (68 bytes).
    Removing stm32g4xx_hal_pwr.o(i.HAL_PWR_EnterSTANDBYMode), (36 bytes).
    Removing stm32g4xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode), (16 bytes).
    Removing stm32g4xx_hal_pwr.o(i.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_ConfigPVM), (400 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBatteryCharging), (16 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableGPIOPullDown), (104 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableGPIOPullUp), (100 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableInternalWakeUpLine), (16 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableLowPowerRunMode), (64 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_DisablePVM1), (16 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_DisablePVM2), (16 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_DisablePVM3), (16 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_DisablePVM4), (16 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_DisablePullUpPullDownConfig), (16 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableSRAM2ContentRetention), (16 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableUCPDStandbyMode), (16 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBatteryCharging), (28 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableGPIOPullDown), (152 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableGPIOPullUp), (152 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableInternalWakeUpLine), (16 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableLowPowerRunMode), (16 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnablePVM1), (16 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnablePVM2), (16 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnablePVM3), (16 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnablePVM4), (16 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnablePullUpPullDownConfig), (16 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableSRAM2ContentRetention), (16 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableUCPDDeadBattery), (16 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableUCPDStandbyMode), (16 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnterSHUTDOWNMode), (36 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnterSTOP0Mode), (52 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_EnterSTOP1Mode), (56 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_GetVoltageRange), (40 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_PVD_PVM_IRQHandler), (88 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_PVM1Callback), (2 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_PVM2Callback), (2 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_PVM3Callback), (2 bytes).
    Removing stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_PVM4Callback), (2 bytes).
    Removing stm32g4xx_hal_cortex.o(.rev16_text), (4 bytes).
    Removing stm32g4xx_hal_cortex.o(.revsh_text), (4 bytes).
    Removing stm32g4xx_hal_cortex.o(.rrx_text), (6 bytes).
    Removing stm32g4xx_hal_cortex.o(i.HAL_MPU_ConfigRegion), (84 bytes).
    Removing stm32g4xx_hal_cortex.o(i.HAL_MPU_Disable), (16 bytes).
    Removing stm32g4xx_hal_cortex.o(i.HAL_MPU_DisableRegion), (24 bytes).
    Removing stm32g4xx_hal_cortex.o(i.HAL_MPU_Enable), (24 bytes).
    Removing stm32g4xx_hal_cortex.o(i.HAL_MPU_EnableRegion), (24 bytes).
    Removing stm32g4xx_hal_cortex.o(i.HAL_NVIC_ClearPendingIRQ), (26 bytes).
    Removing stm32g4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ), (34 bytes).
    Removing stm32g4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ), (26 bytes).
    Removing stm32g4xx_hal_cortex.o(i.HAL_NVIC_GetActive), (36 bytes).
    Removing stm32g4xx_hal_cortex.o(i.HAL_NVIC_GetPendingIRQ), (36 bytes).
    Removing stm32g4xx_hal_cortex.o(i.HAL_NVIC_GetPriority), (82 bytes).
    Removing stm32g4xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping), (16 bytes).
    Removing stm32g4xx_hal_cortex.o(i.HAL_NVIC_SetPendingIRQ), (26 bytes).
    Removing stm32g4xx_hal_cortex.o(i.HAL_NVIC_SystemReset), (36 bytes).
    Removing stm32g4xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig), (24 bytes).
    Removing stm32g4xx_hal_cortex.o(i.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32g4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing system_stm32g4xx.o(.rev16_text), (4 bytes).
    Removing system_stm32g4xx.o(.revsh_text), (4 bytes).
    Removing system_stm32g4xx.o(.rrx_text), (6 bytes).
    Removing system_stm32g4xx.o(i.SystemCoreClockUpdate), (124 bytes).
    Removing system_stm32g4xx.o(.constdata), (8 bytes).
    Removing scheduler.o(.rev16_text), (4 bytes).
    Removing scheduler.o(.revsh_text), (4 bytes).
    Removing scheduler.o(.rrx_text), (6 bytes).
    Removing key_app.o(.rev16_text), (4 bytes).
    Removing key_app.o(.revsh_text), (4 bytes).
    Removing key_app.o(.rrx_text), (6 bytes).
    Removing oled_app.o(.rev16_text), (4 bytes).
    Removing oled_app.o(.revsh_text), (4 bytes).
    Removing oled_app.o(.rrx_text), (6 bytes).
    Removing oled.o(.rev16_text), (4 bytes).
    Removing oled.o(.revsh_text), (4 bytes).
    Removing oled.o(.rrx_text), (6 bytes).
    Removing oled.o(i.OLED_Allfill), (52 bytes).
    Removing oled.o(i.OLED_Display_Off), (24 bytes).
    Removing oled.o(i.OLED_Display_On), (24 bytes).
    Removing oled.o(i.OLED_ShowFloat), (266 bytes).
    Removing oled.o(i.OLED_ShowHanzi), (76 bytes).
    Removing oled.o(i.OLED_ShowHzbig), (136 bytes).
    Removing oled.o(i.OLED_ShowNum), (114 bytes).
    Removing oled.o(i.OLED_ShowPic), (64 bytes).

359 unused section(s) (total 24674 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../Core/Src/gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ../Core/Src/i2c.c                        0x00000000   Number         0  i2c.o ABSOLUTE
    ../Core/Src/main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ../Core/Src/stm32g4xx_hal_msp.c          0x00000000   Number         0  stm32g4xx_hal_msp.o ABSOLUTE
    ../Core/Src/stm32g4xx_it.c               0x00000000   Number         0  stm32g4xx_it.o ABSOLUTE
    ../Core/Src/system_stm32g4xx.c           0x00000000   Number         0  system_stm32g4xx.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal.c 0x00000000   Number         0  stm32g4xx_hal.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_cortex.c 0x00000000   Number         0  stm32g4xx_hal_cortex.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_dma.c 0x00000000   Number         0  stm32g4xx_hal_dma.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_dma_ex.c 0x00000000   Number         0  stm32g4xx_hal_dma_ex.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_exti.c 0x00000000   Number         0  stm32g4xx_hal_exti.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_flash.c 0x00000000   Number         0  stm32g4xx_hal_flash.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_flash_ex.c 0x00000000   Number         0  stm32g4xx_hal_flash_ex.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_flash_ramfunc.c 0x00000000   Number         0  stm32g4xx_hal_flash_ramfunc.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_gpio.c 0x00000000   Number         0  stm32g4xx_hal_gpio.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_i2c.c 0x00000000   Number         0  stm32g4xx_hal_i2c.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_i2c_ex.c 0x00000000   Number         0  stm32g4xx_hal_i2c_ex.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_pwr.c 0x00000000   Number         0  stm32g4xx_hal_pwr.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32g4xx_hal_pwr_ex.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_rcc.c 0x00000000   Number         0  stm32g4xx_hal_rcc.o ABSOLUTE
    ../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32g4xx_hal_rcc_ex.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr_w.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ..\APP\key_app.c                         0x00000000   Number         0  key_app.o ABSOLUTE
    ..\APP\oled_app.c                        0x00000000   Number         0  oled_app.o ABSOLUTE
    ..\APP\scheduler.c                       0x00000000   Number         0  scheduler.o ABSOLUTE
    ..\Core\Src\gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ..\Core\Src\i2c.c                        0x00000000   Number         0  i2c.o ABSOLUTE
    ..\Core\Src\main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ..\Core\Src\stm32g4xx_hal_msp.c          0x00000000   Number         0  stm32g4xx_hal_msp.o ABSOLUTE
    ..\Core\Src\stm32g4xx_it.c               0x00000000   Number         0  stm32g4xx_it.o ABSOLUTE
    ..\Core\Src\system_stm32g4xx.c           0x00000000   Number         0  system_stm32g4xx.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal.c 0x00000000   Number         0  stm32g4xx_hal.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_cortex.c 0x00000000   Number         0  stm32g4xx_hal_cortex.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_dma.c 0x00000000   Number         0  stm32g4xx_hal_dma.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_dma_ex.c 0x00000000   Number         0  stm32g4xx_hal_dma_ex.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_exti.c 0x00000000   Number         0  stm32g4xx_hal_exti.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_flash.c 0x00000000   Number         0  stm32g4xx_hal_flash.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_flash_ex.c 0x00000000   Number         0  stm32g4xx_hal_flash_ex.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_flash_ramfunc.c 0x00000000   Number         0  stm32g4xx_hal_flash_ramfunc.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_gpio.c 0x00000000   Number         0  stm32g4xx_hal_gpio.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_i2c.c 0x00000000   Number         0  stm32g4xx_hal_i2c.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_i2c_ex.c 0x00000000   Number         0  stm32g4xx_hal_i2c_ex.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_pwr.c 0x00000000   Number         0  stm32g4xx_hal_pwr.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32g4xx_hal_pwr_ex.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_rcc.c 0x00000000   Number         0  stm32g4xx_hal_rcc.o ABSOLUTE
    ..\Drivers\STM32G4xx_HAL_Driver\Src\stm32g4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32g4xx_hal_rcc_ex.o ABSOLUTE
    ..\Hardware\oled\oled.c                  0x00000000   Number         0  oled.o ABSOLUTE
    ..\\APP\\key_app.c                       0x00000000   Number         0  key_app.o ABSOLUTE
    ..\\APP\\oled_app.c                      0x00000000   Number         0  oled_app.o ABSOLUTE
    ..\\APP\\scheduler.c                     0x00000000   Number         0  scheduler.o ABSOLUTE
    ..\\Hardware\\oled\\oled.c               0x00000000   Number         0  oled.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    startup_stm32g474xx.s                    0x00000000   Number         0  startup_stm32g474xx.o ABSOLUTE
    RESET                                    0x08000000   Section      472  startup_stm32g474xx.o(RESET)
    !!!main                                  0x080001d8   Section        8  __main.o(!!!main)
    !!!scatter                               0x080001e0   Section       52  __scatter.o(!!!scatter)
    !!handler_copy                           0x08000214   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x08000230   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$libinit$$00000000          0x0800024c   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000001          0x0800024e   Section        4  libinit2.o(.ARM.Collect$$libinit$$00000001)
    .ARM.Collect$$libinit$$00000004          0x08000252   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x08000252   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x08000252   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x08000252   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$00000011          0x08000252   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000013          0x08000252   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x08000252   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000017          0x08000252   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x08000252   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x08000252   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x08000252   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x08000252   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x08000252   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x08000252   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x08000252   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x08000252   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x08000252   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x08000252   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x08000252   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x08000252   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x08000254   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x08000256   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x08000256   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000007      0x08000256   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    .ARM.Collect$$libshutdown$$0000000A      0x08000256   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    .ARM.Collect$$libshutdown$$0000000C      0x08000256   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000F      0x08000256   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    .ARM.Collect$$libshutdown$$00000010      0x08000256   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    .ARM.Collect$$rtentry$$00000000          0x08000258   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x08000258   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x08000258   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x0800025e   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x0800025e   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x08000262   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x08000262   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x0800026a   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x0800026c   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x0800026c   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x08000270   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x08000278   Section       64  startup_stm32g474xx.o(.text)
    $v0                                      0x08000278   Number         0  startup_stm32g474xx.o(.text)
    .text                                    0x080002b8   Section       78  rt_memclr_w.o(.text)
    .text                                    0x08000306   Section        0  heapauxi.o(.text)
    .text                                    0x0800030c   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x08000356   Section        0  exit.o(.text)
    .text                                    0x08000368   Section        8  libspace.o(.text)
    .text                                    0x08000370   Section        0  sys_exit.o(.text)
    .text                                    0x0800037c   Section        2  use_no_semi.o(.text)
    .text                                    0x0800037e   Section        0  indicate_semi.o(.text)
    i.BusFault_Handler                       0x0800037e   Section        0  stm32g4xx_it.o(i.BusFault_Handler)
    i.DebugMon_Handler                       0x08000380   Section        0  stm32g4xx_it.o(i.DebugMon_Handler)
    i.Error_Handler                          0x08000382   Section        0  main.o(i.Error_Handler)
    i.HAL_Delay                              0x08000388   Section        0  stm32g4xx_hal.o(i.HAL_Delay)
    i.HAL_GPIO_Init                          0x080003ac   Section        0  stm32g4xx_hal_gpio.o(i.HAL_GPIO_Init)
    i.HAL_GPIO_ReadPin                       0x080005f4   Section        0  stm32g4xx_hal_gpio.o(i.HAL_GPIO_ReadPin)
    i.HAL_GetTick                            0x08000600   Section        0  stm32g4xx_hal.o(i.HAL_GetTick)
    i.HAL_I2CEx_ConfigAnalogFilter           0x0800060c   Section        0  stm32g4xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigAnalogFilter)
    i.HAL_I2CEx_ConfigDigitalFilter          0x08000662   Section        0  stm32g4xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigDigitalFilter)
    i.HAL_I2C_Init                           0x080006b4   Section        0  stm32g4xx_hal_i2c.o(i.HAL_I2C_Init)
    i.HAL_I2C_Mem_Write                      0x08000770   Section        0  stm32g4xx_hal_i2c.o(i.HAL_I2C_Mem_Write)
    i.HAL_I2C_MspInit                        0x080008c8   Section        0  i2c.o(i.HAL_I2C_MspInit)
    i.HAL_IncTick                            0x08000948   Section        0  stm32g4xx_hal.o(i.HAL_IncTick)
    i.HAL_Init                               0x08000958   Section        0  stm32g4xx_hal.o(i.HAL_Init)
    i.HAL_InitTick                           0x08000978   Section        0  stm32g4xx_hal.o(i.HAL_InitTick)
    i.HAL_MspInit                            0x080009bc   Section        0  stm32g4xx_hal_msp.o(i.HAL_MspInit)
    i.HAL_NVIC_SetPriority                   0x080009ec   Section        0  stm32g4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    i.HAL_NVIC_SetPriorityGrouping           0x08000a2c   Section        0  stm32g4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    i.HAL_PWREx_ControlVoltageScaling        0x08000a50   Section        0  stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling)
    i.HAL_PWREx_DisableUCPDDeadBattery       0x08000b0c   Section        0  stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableUCPDDeadBattery)
    i.HAL_RCCEx_PeriphCLKConfig              0x08000b1c   Section        0  stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig)
    i.HAL_RCC_ClockConfig                    0x08000e1c   Section        0  stm32g4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    i.HAL_RCC_GetSysClockFreq                0x08000ff8   Section        0  stm32g4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    i.HAL_RCC_OscConfig                      0x08001064   Section        0  stm32g4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    i.HAL_SYSTICK_Config                     0x08001468   Section        0  stm32g4xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    i.HardFault_Handler                      0x08001490   Section        0  stm32g4xx_it.o(i.HardFault_Handler)
    i.I2C_Flush_TXDR                         0x08001492   Section        0  stm32g4xx_hal_i2c.o(i.I2C_Flush_TXDR)
    I2C_Flush_TXDR                           0x08001493   Thumb Code    34  stm32g4xx_hal_i2c.o(i.I2C_Flush_TXDR)
    i.I2C_IsErrorOccurred                    0x080014b4   Section        0  stm32g4xx_hal_i2c.o(i.I2C_IsErrorOccurred)
    I2C_IsErrorOccurred                      0x080014b5   Thumb Code   264  stm32g4xx_hal_i2c.o(i.I2C_IsErrorOccurred)
    i.I2C_RequestMemoryWrite                 0x080015c0   Section        0  stm32g4xx_hal_i2c.o(i.I2C_RequestMemoryWrite)
    I2C_RequestMemoryWrite                   0x080015c1   Thumb Code    94  stm32g4xx_hal_i2c.o(i.I2C_RequestMemoryWrite)
    i.I2C_TransferConfig                     0x08001624   Section        0  stm32g4xx_hal_i2c.o(i.I2C_TransferConfig)
    I2C_TransferConfig                       0x08001625   Thumb Code    44  stm32g4xx_hal_i2c.o(i.I2C_TransferConfig)
    i.I2C_WaitOnFlagUntilTimeout             0x08001654   Section        0  stm32g4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout)
    I2C_WaitOnFlagUntilTimeout               0x08001655   Thumb Code   122  stm32g4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout)
    i.I2C_WaitOnSTOPFlagUntilTimeout         0x080016ce   Section        0  stm32g4xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout)
    I2C_WaitOnSTOPFlagUntilTimeout           0x080016cf   Thumb Code    86  stm32g4xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout)
    i.I2C_WaitOnTXISFlagUntilTimeout         0x08001724   Section        0  stm32g4xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout)
    I2C_WaitOnTXISFlagUntilTimeout           0x08001725   Thumb Code    90  stm32g4xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout)
    i.MX_GPIO_Init                           0x08001780   Section        0  gpio.o(i.MX_GPIO_Init)
    i.MX_I2C1_Init                           0x080017cc   Section        0  i2c.o(i.MX_I2C1_Init)
    i.MemManage_Handler                      0x08001824   Section        0  stm32g4xx_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x08001826   Section        0  stm32g4xx_it.o(i.NMI_Handler)
    i.OLED_App_Init                          0x08001828   Section        0  oled_app.o(i.OLED_App_Init)
    i.OLED_App_ShowHello                     0x08001838   Section        0  oled_app.o(i.OLED_App_ShowHello)
    i.OLED_Clear                             0x0800184c   Section        0  oled.o(i.OLED_Clear)
    i.OLED_Init                              0x08001880   Section        0  oled.o(i.OLED_Init)
    i.OLED_Set_Position                      0x080018b0   Section        0  oled.o(i.OLED_Set_Position)
    i.OLED_ShowChar                          0x080018d4   Section        0  oled.o(i.OLED_ShowChar)
    i.OLED_ShowStr                           0x0800195c   Section        0  oled.o(i.OLED_ShowStr)
    i.OLED_Write_cmd                         0x08001994   Section        0  oled.o(i.OLED_Write_cmd)
    i.OLED_Write_data                        0x080019b8   Section        0  oled.o(i.OLED_Write_data)
    i.OLED_task                              0x080019dc   Section        0  oled_app.o(i.OLED_task)
    i.PendSV_Handler                         0x080019de   Section        0  stm32g4xx_it.o(i.PendSV_Handler)
    i.SVC_Handler                            0x080019e0   Section        0  stm32g4xx_it.o(i.SVC_Handler)
    i.SysTick_Handler                        0x080019e2   Section        0  stm32g4xx_it.o(i.SysTick_Handler)
    i.SystemClock_Config                     0x080019e6   Section        0  main.o(i.SystemClock_Config)
    i.SystemInit                             0x08001a50   Section        0  system_stm32g4xx.o(i.SystemInit)
    i.UsageFault_Handler                     0x08001a60   Section        0  stm32g4xx_it.o(i.UsageFault_Handler)
    i.__NVIC_SetPriority                     0x08001a62   Section        0  stm32g4xx_hal_cortex.o(i.__NVIC_SetPriority)
    __NVIC_SetPriority                       0x08001a63   Thumb Code    32  stm32g4xx_hal_cortex.o(i.__NVIC_SetPriority)
    i.key_read                               0x08001a82   Section        0  key_app.o(i.key_read)
    i.key_task                               0x08001a98   Section        0  key_app.o(i.key_task)
    i.main                                   0x08001ab4   Section        0  main.o(i.main)
    i.scheduler_init                         0x08001ad8   Section        0  scheduler.o(i.scheduler_init)
    i.scheduler_run                          0x08001ae4   Section        0  scheduler.o(i.scheduler_run)
    x$fpl$fpinit                             0x08001b20   Section       10  fpinit.o(x$fpl$fpinit)
    $v0                                      0x08001b20   Number         0  fpinit.o(x$fpl$fpinit)
    .constdata                               0x08001b2a   Section       16  system_stm32g4xx.o(.constdata)
    .constdata                               0x08001b3a   Section     2712  oled.o(.constdata)
    F8X16                                    0x08001d62   Data        1520  oled.o(.constdata)
    .data                                    0x20000000   Section       12  stm32g4xx_hal.o(.data)
    .data                                    0x2000000c   Section        4  system_stm32g4xx.o(.data)
    .data                                    0x20000010   Section       28  scheduler.o(.data)
    scheduler_task                           0x20000014   Data          24  scheduler.o(.data)
    .data                                    0x2000002c   Section        4  key_app.o(.data)
    .data                                    0x20000030   Section       22  oled.o(.data)
    .bss                                     0x20000048   Section       84  i2c.o(.bss)
    .bss                                     0x2000009c   Section       96  libspace.o(.bss)
    HEAP                                     0x20000100   Section      512  startup_stm32g474xx.o(HEAP)
    Heap_Mem                                 0x20000100   Data         512  startup_stm32g474xx.o(HEAP)
    STACK                                    0x20000300   Section     1024  startup_stm32g474xx.o(STACK)
    Stack_Mem                                0x20000300   Data        1024  startup_stm32g474xx.o(STACK)
    __initial_sp                             0x20000700   Data           0  startup_stm32g474xx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OSPACE$ROPI$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __rt_locale                               - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_ctype                             - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_numeric                           - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x000001d8   Number         0  startup_stm32g474xx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32g474xx.o(RESET)
    __Vectors_End                            0x080001d8   Data           0  startup_stm32g474xx.o(RESET)
    __main                                   0x080001d9   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x080001e1   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x080001e1   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x080001e1   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x080001ef   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x08000215   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x08000231   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    __rt_lib_init                            0x0800024d   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x0800024f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000001)
    __rt_lib_init_alloca_1                   0x08000253   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x08000253   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x08000253   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x08000253   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x08000253   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x08000253   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_trap_1                  0x08000253   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x08000253   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_heap_1                     0x08000253   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_collate_1               0x08000253   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_1                 0x08000253   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x08000253   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_1               0x08000253   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x08000253   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_preinit_1                  0x08000253   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x08000253   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_return                     0x08000253   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x08000253   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x08000253   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_init_user_alloc_1               0x08000253   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_shutdown                        0x08000255   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x08000257   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x08000257   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    __rt_lib_shutdown_heap_1                 0x08000257   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    __rt_lib_shutdown_return                 0x08000257   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    __rt_lib_shutdown_signal_1               0x08000257   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    __rt_lib_shutdown_stdio_1                0x08000257   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_user_alloc_1           0x08000257   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_entry                               0x08000259   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x08000259   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x08000259   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x0800025f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x0800025f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x08000263   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x08000263   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x0800026b   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x0800026d   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x0800026d   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x08000271   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x08000279   Thumb Code     8  startup_stm32g474xx.o(.text)
    ADC1_2_IRQHandler                        0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    ADC3_IRQHandler                          0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    ADC4_IRQHandler                          0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    ADC5_IRQHandler                          0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    COMP1_2_3_IRQHandler                     0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    COMP4_5_6_IRQHandler                     0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    COMP7_IRQHandler                         0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    CORDIC_IRQHandler                        0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    CRS_IRQHandler                           0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    DMA1_Channel1_IRQHandler                 0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    DMA1_Channel2_IRQHandler                 0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    DMA1_Channel3_IRQHandler                 0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    DMA1_Channel4_IRQHandler                 0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    DMA1_Channel5_IRQHandler                 0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    DMA1_Channel6_IRQHandler                 0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    DMA1_Channel7_IRQHandler                 0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    DMA1_Channel8_IRQHandler                 0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    DMA2_Channel1_IRQHandler                 0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    DMA2_Channel2_IRQHandler                 0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    DMA2_Channel3_IRQHandler                 0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    DMA2_Channel4_IRQHandler                 0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    DMA2_Channel5_IRQHandler                 0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    DMA2_Channel6_IRQHandler                 0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    DMA2_Channel7_IRQHandler                 0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    DMA2_Channel8_IRQHandler                 0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    DMAMUX_OVR_IRQHandler                    0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    EXTI0_IRQHandler                         0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    EXTI15_10_IRQHandler                     0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    EXTI1_IRQHandler                         0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    EXTI2_IRQHandler                         0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    EXTI3_IRQHandler                         0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    EXTI4_IRQHandler                         0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    EXTI9_5_IRQHandler                       0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    FDCAN1_IT0_IRQHandler                    0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    FDCAN1_IT1_IRQHandler                    0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    FDCAN2_IT0_IRQHandler                    0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    FDCAN2_IT1_IRQHandler                    0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    FDCAN3_IT0_IRQHandler                    0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    FDCAN3_IT1_IRQHandler                    0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    FLASH_IRQHandler                         0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    FMAC_IRQHandler                          0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    FMC_IRQHandler                           0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    FPU_IRQHandler                           0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    HRTIM1_FLT_IRQHandler                    0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    HRTIM1_Master_IRQHandler                 0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    HRTIM1_TIMA_IRQHandler                   0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    HRTIM1_TIMB_IRQHandler                   0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    HRTIM1_TIMC_IRQHandler                   0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    HRTIM1_TIMD_IRQHandler                   0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    HRTIM1_TIME_IRQHandler                   0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    HRTIM1_TIMF_IRQHandler                   0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    I2C1_ER_IRQHandler                       0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    I2C1_EV_IRQHandler                       0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    I2C2_ER_IRQHandler                       0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    I2C2_EV_IRQHandler                       0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    I2C3_ER_IRQHandler                       0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    I2C3_EV_IRQHandler                       0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    I2C4_ER_IRQHandler                       0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    I2C4_EV_IRQHandler                       0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    LPTIM1_IRQHandler                        0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    LPUART1_IRQHandler                       0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    PVD_PVM_IRQHandler                       0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    QUADSPI_IRQHandler                       0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    RCC_IRQHandler                           0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    RNG_IRQHandler                           0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    RTC_Alarm_IRQHandler                     0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    RTC_TAMP_LSECSS_IRQHandler               0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    RTC_WKUP_IRQHandler                      0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    SAI1_IRQHandler                          0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    SPI1_IRQHandler                          0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    SPI2_IRQHandler                          0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    SPI3_IRQHandler                          0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    SPI4_IRQHandler                          0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    TIM1_BRK_TIM15_IRQHandler                0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    TIM1_CC_IRQHandler                       0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    TIM1_TRG_COM_TIM17_IRQHandler            0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    TIM1_UP_TIM16_IRQHandler                 0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    TIM20_BRK_IRQHandler                     0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    TIM20_CC_IRQHandler                      0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    TIM20_TRG_COM_IRQHandler                 0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    TIM20_UP_IRQHandler                      0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    TIM2_IRQHandler                          0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    TIM3_IRQHandler                          0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    TIM4_IRQHandler                          0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    TIM5_IRQHandler                          0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    TIM6_DAC_IRQHandler                      0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    TIM7_DAC_IRQHandler                      0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    TIM8_BRK_IRQHandler                      0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    TIM8_CC_IRQHandler                       0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    TIM8_TRG_COM_IRQHandler                  0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    TIM8_UP_IRQHandler                       0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    UART4_IRQHandler                         0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    UART5_IRQHandler                         0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    UCPD1_IRQHandler                         0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    USART1_IRQHandler                        0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    USART2_IRQHandler                        0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    USART3_IRQHandler                        0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    USBWakeUp_IRQHandler                     0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    USB_HP_IRQHandler                        0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    USB_LP_IRQHandler                        0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    WWDG_IRQHandler                          0x08000293   Thumb Code     0  startup_stm32g474xx.o(.text)
    __user_initial_stackheap                 0x08000295   Thumb Code     0  startup_stm32g474xx.o(.text)
    __aeabi_memclr4                          0x080002b9   Thumb Code     0  rt_memclr_w.o(.text)
    __aeabi_memclr8                          0x080002b9   Thumb Code     0  rt_memclr_w.o(.text)
    __rt_memclr_w                            0x080002b9   Thumb Code    78  rt_memclr_w.o(.text)
    _memset_w                                0x080002bd   Thumb Code     0  rt_memclr_w.o(.text)
    __use_two_region_memory                  0x08000307   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x08000309   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x0800030b   Thumb Code     2  heapauxi.o(.text)
    __user_setup_stackheap                   0x0800030d   Thumb Code    74  sys_stackheap_outer.o(.text)
    exit                                     0x08000357   Thumb Code    18  exit.o(.text)
    __user_libspace                          0x08000369   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x08000369   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x08000369   Thumb Code     0  libspace.o(.text)
    _sys_exit                                0x08000371   Thumb Code     8  sys_exit.o(.text)
    __I$use$semihosting                      0x0800037d   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x0800037d   Thumb Code     2  use_no_semi.o(.text)
    BusFault_Handler                         0x0800037f   Thumb Code     2  stm32g4xx_it.o(i.BusFault_Handler)
    __semihosting_library_function           0x0800037f   Thumb Code     0  indicate_semi.o(.text)
    DebugMon_Handler                         0x08000381   Thumb Code     2  stm32g4xx_it.o(i.DebugMon_Handler)
    Error_Handler                            0x08000383   Thumb Code     4  main.o(i.Error_Handler)
    HAL_Delay                                0x08000389   Thumb Code    32  stm32g4xx_hal.o(i.HAL_Delay)
    HAL_GPIO_Init                            0x080003ad   Thumb Code   550  stm32g4xx_hal_gpio.o(i.HAL_GPIO_Init)
    HAL_GPIO_ReadPin                         0x080005f5   Thumb Code    10  stm32g4xx_hal_gpio.o(i.HAL_GPIO_ReadPin)
    HAL_GetTick                              0x08000601   Thumb Code     6  stm32g4xx_hal.o(i.HAL_GetTick)
    HAL_I2CEx_ConfigAnalogFilter             0x0800060d   Thumb Code    86  stm32g4xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigAnalogFilter)
    HAL_I2CEx_ConfigDigitalFilter            0x08000663   Thumb Code    82  stm32g4xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigDigitalFilter)
    HAL_I2C_Init                             0x080006b5   Thumb Code   184  stm32g4xx_hal_i2c.o(i.HAL_I2C_Init)
    HAL_I2C_Mem_Write                        0x08000771   Thumb Code   340  stm32g4xx_hal_i2c.o(i.HAL_I2C_Mem_Write)
    HAL_I2C_MspInit                          0x080008c9   Thumb Code   114  i2c.o(i.HAL_I2C_MspInit)
    HAL_IncTick                              0x08000949   Thumb Code    12  stm32g4xx_hal.o(i.HAL_IncTick)
    HAL_Init                                 0x08000959   Thumb Code    30  stm32g4xx_hal.o(i.HAL_Init)
    HAL_InitTick                             0x08000979   Thumb Code    58  stm32g4xx_hal.o(i.HAL_InitTick)
    HAL_MspInit                              0x080009bd   Thumb Code    42  stm32g4xx_hal_msp.o(i.HAL_MspInit)
    HAL_NVIC_SetPriority                     0x080009ed   Thumb Code    60  stm32g4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    HAL_NVIC_SetPriorityGrouping             0x08000a2d   Thumb Code    26  stm32g4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    HAL_PWREx_ControlVoltageScaling          0x08000a51   Thumb Code   174  stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling)
    HAL_PWREx_DisableUCPDDeadBattery         0x08000b0d   Thumb Code    12  stm32g4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableUCPDDeadBattery)
    HAL_RCCEx_PeriphCLKConfig                0x08000b1d   Thumb Code   758  stm32g4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig)
    HAL_RCC_ClockConfig                      0x08000e1d   Thumb Code   444  stm32g4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    HAL_RCC_GetSysClockFreq                  0x08000ff9   Thumb Code    98  stm32g4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    HAL_RCC_OscConfig                        0x08001065   Thumb Code  1028  stm32g4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    HAL_SYSTICK_Config                       0x08001469   Thumb Code    40  stm32g4xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    HardFault_Handler                        0x08001491   Thumb Code     2  stm32g4xx_it.o(i.HardFault_Handler)
    MX_GPIO_Init                             0x08001781   Thumb Code    70  gpio.o(i.MX_GPIO_Init)
    MX_I2C1_Init                             0x080017cd   Thumb Code    76  i2c.o(i.MX_I2C1_Init)
    MemManage_Handler                        0x08001825   Thumb Code     2  stm32g4xx_it.o(i.MemManage_Handler)
    NMI_Handler                              0x08001827   Thumb Code     2  stm32g4xx_it.o(i.NMI_Handler)
    OLED_App_Init                            0x08001829   Thumb Code    14  oled_app.o(i.OLED_App_Init)
    OLED_App_ShowHello                       0x08001839   Thumb Code    12  oled_app.o(i.OLED_App_ShowHello)
    OLED_Clear                               0x0800184d   Thumb Code    52  oled.o(i.OLED_Clear)
    OLED_Init                                0x08001881   Thumb Code    42  oled.o(i.OLED_Init)
    OLED_Set_Position                        0x080018b1   Thumb Code    34  oled.o(i.OLED_Set_Position)
    OLED_ShowChar                            0x080018d5   Thumb Code   126  oled.o(i.OLED_ShowChar)
    OLED_ShowStr                             0x0800195d   Thumb Code    54  oled.o(i.OLED_ShowStr)
    OLED_Write_cmd                           0x08001995   Thumb Code    32  oled.o(i.OLED_Write_cmd)
    OLED_Write_data                          0x080019b9   Thumb Code    32  oled.o(i.OLED_Write_data)
    OLED_task                                0x080019dd   Thumb Code     2  oled_app.o(i.OLED_task)
    PendSV_Handler                           0x080019df   Thumb Code     2  stm32g4xx_it.o(i.PendSV_Handler)
    SVC_Handler                              0x080019e1   Thumb Code     2  stm32g4xx_it.o(i.SVC_Handler)
    SysTick_Handler                          0x080019e3   Thumb Code     4  stm32g4xx_it.o(i.SysTick_Handler)
    SystemClock_Config                       0x080019e7   Thumb Code   104  main.o(i.SystemClock_Config)
    SystemInit                               0x08001a51   Thumb Code    12  system_stm32g4xx.o(i.SystemInit)
    UsageFault_Handler                       0x08001a61   Thumb Code     2  stm32g4xx_it.o(i.UsageFault_Handler)
    key_read                                 0x08001a83   Thumb Code    22  key_app.o(i.key_read)
    key_task                                 0x08001a99   Thumb Code    24  key_app.o(i.key_task)
    main                                     0x08001ab5   Thumb Code    34  main.o(i.main)
    scheduler_init                           0x08001ad9   Thumb Code     8  scheduler.o(i.scheduler_init)
    scheduler_run                            0x08001ae5   Thumb Code    56  scheduler.o(i.scheduler_run)
    _fp_init                                 0x08001b21   Thumb Code    10  fpinit.o(x$fpl$fpinit)
    __fplib_config_fpu_vfp                   0x08001b29   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fplib_config_pureend_doubles           0x08001b29   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    AHBPrescTable                            0x08001b2a   Data          16  system_stm32g4xx.o(.constdata)
    F6X8                                     0x08001b3a   Data         552  oled.o(.constdata)
    Hzk                                      0x08002352   Data         128  oled.o(.constdata)
    Hzb                                      0x080023d2   Data         512  oled.o(.constdata)
    Region$$Table$$Base                      0x080025d4   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x080025f4   Number         0  anon$$obj.o(Region$$Table)
    uwTickPrio                               0x20000000   Data           4  stm32g4xx_hal.o(.data)
    uwTickFreq                               0x20000004   Data           4  stm32g4xx_hal.o(.data)
    uwTick                                   0x20000008   Data           4  stm32g4xx_hal.o(.data)
    SystemCoreClock                          0x2000000c   Data           4  system_stm32g4xx.o(.data)
    task_num                                 0x20000010   Data           1  scheduler.o(.data)
    key_val                                  0x2000002c   Data           1  key_app.o(.data)
    key_old                                  0x2000002d   Data           1  key_app.o(.data)
    key_down                                 0x2000002e   Data           1  key_app.o(.data)
    key_up                                   0x2000002f   Data           1  key_app.o(.data)
    initcmd1                                 0x20000030   Data          22  oled.o(.data)
    hi2c1                                    0x20000048   Data          84  i2c.o(.bss)
    __libspace_start                         0x2000009c   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x200000fc   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x080001d9

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x0000263c, Max: 0x00080000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x000025f4, Max: 0x00080000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x000001d8   Data   RO            3    RESET               startup_stm32g474xx.o
    0x080001d8   0x080001d8   0x00000008   Code   RO         2668  * !!!main             c_w.l(__main.o)
    0x080001e0   0x080001e0   0x00000034   Code   RO         2825    !!!scatter          c_w.l(__scatter.o)
    0x08000214   0x08000214   0x0000001a   Code   RO         2827    !!handler_copy      c_w.l(__scatter_copy.o)
    0x0800022e   0x0800022e   0x00000002   PAD
    0x08000230   0x08000230   0x0000001c   Code   RO         2829    !!handler_zi        c_w.l(__scatter_zi.o)
    0x0800024c   0x0800024c   0x00000002   Code   RO         2695    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x0800024e   0x0800024e   0x00000004   Code   RO         2701    .ARM.Collect$$libinit$$00000001  c_w.l(libinit2.o)
    0x08000252   0x08000252   0x00000000   Code   RO         2704    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x08000252   0x08000252   0x00000000   Code   RO         2707    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x08000252   0x08000252   0x00000000   Code   RO         2709    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x08000252   0x08000252   0x00000000   Code   RO         2711    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x08000252   0x08000252   0x00000000   Code   RO         2714    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x08000252   0x08000252   0x00000000   Code   RO         2716    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x08000252   0x08000252   0x00000000   Code   RO         2718    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x08000252   0x08000252   0x00000000   Code   RO         2720    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x08000252   0x08000252   0x00000000   Code   RO         2722    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x08000252   0x08000252   0x00000000   Code   RO         2724    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x08000252   0x08000252   0x00000000   Code   RO         2726    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x08000252   0x08000252   0x00000000   Code   RO         2728    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x08000252   0x08000252   0x00000000   Code   RO         2730    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x08000252   0x08000252   0x00000000   Code   RO         2732    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x08000252   0x08000252   0x00000000   Code   RO         2734    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x08000252   0x08000252   0x00000000   Code   RO         2738    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x08000252   0x08000252   0x00000000   Code   RO         2740    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x08000252   0x08000252   0x00000000   Code   RO         2742    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x08000252   0x08000252   0x00000000   Code   RO         2744    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x08000252   0x08000252   0x00000002   Code   RO         2745    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x08000254   0x08000254   0x00000002   Code   RO         2765    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x08000256   0x08000256   0x00000000   Code   RO         2778    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x08000256   0x08000256   0x00000000   Code   RO         2780    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x08000256   0x08000256   0x00000000   Code   RO         2783    .ARM.Collect$$libshutdown$$00000007  c_w.l(libshutdown2.o)
    0x08000256   0x08000256   0x00000000   Code   RO         2786    .ARM.Collect$$libshutdown$$0000000A  c_w.l(libshutdown2.o)
    0x08000256   0x08000256   0x00000000   Code   RO         2788    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x08000256   0x08000256   0x00000000   Code   RO         2791    .ARM.Collect$$libshutdown$$0000000F  c_w.l(libshutdown2.o)
    0x08000256   0x08000256   0x00000002   Code   RO         2792    .ARM.Collect$$libshutdown$$00000010  c_w.l(libshutdown2.o)
    0x08000258   0x08000258   0x00000000   Code   RO         2670    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x08000258   0x08000258   0x00000000   Code   RO         2672    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x08000258   0x08000258   0x00000006   Code   RO         2684    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x0800025e   0x0800025e   0x00000000   Code   RO         2674    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x0800025e   0x0800025e   0x00000004   Code   RO         2675    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x08000262   0x08000262   0x00000000   Code   RO         2677    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x08000262   0x08000262   0x00000008   Code   RO         2678    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x0800026a   0x0800026a   0x00000002   Code   RO         2699    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x0800026c   0x0800026c   0x00000000   Code   RO         2747    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x0800026c   0x0800026c   0x00000004   Code   RO         2748    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x08000270   0x08000270   0x00000006   Code   RO         2749    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x08000276   0x08000276   0x00000002   PAD
    0x08000278   0x08000278   0x00000040   Code   RO            4    .text               startup_stm32g474xx.o
    0x080002b8   0x080002b8   0x0000004e   Code   RO         2664    .text               c_w.l(rt_memclr_w.o)
    0x08000306   0x08000306   0x00000006   Code   RO         2666    .text               c_w.l(heapauxi.o)
    0x0800030c   0x0800030c   0x0000004a   Code   RO         2686    .text               c_w.l(sys_stackheap_outer.o)
    0x08000356   0x08000356   0x00000012   Code   RO         2688    .text               c_w.l(exit.o)
    0x08000368   0x08000368   0x00000008   Code   RO         2696    .text               c_w.l(libspace.o)
    0x08000370   0x08000370   0x0000000c   Code   RO         2757    .text               c_w.l(sys_exit.o)
    0x0800037c   0x0800037c   0x00000002   Code   RO         2768    .text               c_w.l(use_no_semi.o)
    0x0800037e   0x0800037e   0x00000000   Code   RO         2770    .text               c_w.l(indicate_semi.o)
    0x0800037e   0x0800037e   0x00000002   Code   RO          245    i.BusFault_Handler  stm32g4xx_it.o
    0x08000380   0x08000380   0x00000002   Code   RO          246    i.DebugMon_Handler  stm32g4xx_it.o
    0x08000382   0x08000382   0x00000004   Code   RO           13    i.Error_Handler     main.o
    0x08000386   0x08000386   0x00000002   PAD
    0x08000388   0x08000388   0x00000024   Code   RO          877    i.HAL_Delay         stm32g4xx_hal.o
    0x080003ac   0x080003ac   0x00000248   Code   RO         1602    i.HAL_GPIO_Init     stm32g4xx_hal_gpio.o
    0x080005f4   0x080005f4   0x0000000a   Code   RO         1604    i.HAL_GPIO_ReadPin  stm32g4xx_hal_gpio.o
    0x080005fe   0x080005fe   0x00000002   PAD
    0x08000600   0x08000600   0x0000000c   Code   RO          881    i.HAL_GetTick       stm32g4xx_hal.o
    0x0800060c   0x0800060c   0x00000056   Code   RO          816    i.HAL_I2CEx_ConfigAnalogFilter  stm32g4xx_hal_i2c_ex.o
    0x08000662   0x08000662   0x00000052   Code   RO          817    i.HAL_I2CEx_ConfigDigitalFilter  stm32g4xx_hal_i2c_ex.o
    0x080006b4   0x080006b4   0x000000bc   Code   RO          355    i.HAL_I2C_Init      stm32g4xx_hal_i2c.o
    0x08000770   0x08000770   0x00000158   Code   RO          376    i.HAL_I2C_Mem_Write  stm32g4xx_hal_i2c.o
    0x080008c8   0x080008c8   0x00000080   Code   RO          204    i.HAL_I2C_MspInit   i2c.o
    0x08000948   0x08000948   0x00000010   Code   RO          887    i.HAL_IncTick       stm32g4xx_hal.o
    0x08000958   0x08000958   0x0000001e   Code   RO          888    i.HAL_Init          stm32g4xx_hal.o
    0x08000976   0x08000976   0x00000002   PAD
    0x08000978   0x08000978   0x00000044   Code   RO          889    i.HAL_InitTick      stm32g4xx_hal.o
    0x080009bc   0x080009bc   0x00000030   Code   RO          320    i.HAL_MspInit       stm32g4xx_hal_msp.o
    0x080009ec   0x080009ec   0x00000040   Code   RO         2257    i.HAL_NVIC_SetPriority  stm32g4xx_hal_cortex.o
    0x08000a2c   0x08000a2c   0x00000024   Code   RO         2258    i.HAL_NVIC_SetPriorityGrouping  stm32g4xx_hal_cortex.o
    0x08000a50   0x08000a50   0x000000bc   Code   RO         2005    i.HAL_PWREx_ControlVoltageScaling  stm32g4xx_hal_pwr_ex.o
    0x08000b0c   0x08000b0c   0x00000010   Code   RO         2017    i.HAL_PWREx_DisableUCPDDeadBattery  stm32g4xx_hal_pwr_ex.o
    0x08000b1c   0x08000b1c   0x00000300   Code   RO         1248    i.HAL_RCCEx_PeriphCLKConfig  stm32g4xx_hal_rcc_ex.o
    0x08000e1c   0x08000e1c   0x000001dc   Code   RO         1123    i.HAL_RCC_ClockConfig  stm32g4xx_hal_rcc.o
    0x08000ff8   0x08000ff8   0x0000006c   Code   RO         1133    i.HAL_RCC_GetSysClockFreq  stm32g4xx_hal_rcc.o
    0x08001064   0x08001064   0x00000404   Code   RO         1136    i.HAL_RCC_OscConfig  stm32g4xx_hal_rcc.o
    0x08001468   0x08001468   0x00000028   Code   RO         2262    i.HAL_SYSTICK_Config  stm32g4xx_hal_cortex.o
    0x08001490   0x08001490   0x00000002   Code   RO          247    i.HardFault_Handler  stm32g4xx_it.o
    0x08001492   0x08001492   0x00000022   Code   RO          402    i.I2C_Flush_TXDR    stm32g4xx_hal_i2c.o
    0x080014b4   0x080014b4   0x0000010c   Code   RO          410    i.I2C_IsErrorOccurred  stm32g4xx_hal_i2c.o
    0x080015c0   0x080015c0   0x00000064   Code   RO          416    i.I2C_RequestMemoryWrite  stm32g4xx_hal_i2c.o
    0x08001624   0x08001624   0x00000030   Code   RO          419    i.I2C_TransferConfig  stm32g4xx_hal_i2c.o
    0x08001654   0x08001654   0x0000007a   Code   RO          421    i.I2C_WaitOnFlagUntilTimeout  stm32g4xx_hal_i2c.o
    0x080016ce   0x080016ce   0x00000056   Code   RO          423    i.I2C_WaitOnSTOPFlagUntilTimeout  stm32g4xx_hal_i2c.o
    0x08001724   0x08001724   0x0000005a   Code   RO          424    i.I2C_WaitOnTXISFlagUntilTimeout  stm32g4xx_hal_i2c.o
    0x0800177e   0x0800177e   0x00000002   PAD
    0x08001780   0x08001780   0x0000004c   Code   RO          179    i.MX_GPIO_Init      gpio.o
    0x080017cc   0x080017cc   0x00000058   Code   RO          205    i.MX_I2C1_Init      i2c.o
    0x08001824   0x08001824   0x00000002   Code   RO          248    i.MemManage_Handler  stm32g4xx_it.o
    0x08001826   0x08001826   0x00000002   Code   RO          249    i.NMI_Handler       stm32g4xx_it.o
    0x08001828   0x08001828   0x0000000e   Code   RO         2513    i.OLED_App_Init     oled_app.o
    0x08001836   0x08001836   0x00000002   PAD
    0x08001838   0x08001838   0x00000014   Code   RO         2514    i.OLED_App_ShowHello  oled_app.o
    0x0800184c   0x0800184c   0x00000034   Code   RO         2553    i.OLED_Clear        oled.o
    0x08001880   0x08001880   0x00000030   Code   RO         2556    i.OLED_Init         oled.o
    0x080018b0   0x080018b0   0x00000022   Code   RO         2557    i.OLED_Set_Position  oled.o
    0x080018d2   0x080018d2   0x00000002   PAD
    0x080018d4   0x080018d4   0x00000088   Code   RO         2558    i.OLED_ShowChar     oled.o
    0x0800195c   0x0800195c   0x00000036   Code   RO         2564    i.OLED_ShowStr      oled.o
    0x08001992   0x08001992   0x00000002   PAD
    0x08001994   0x08001994   0x00000024   Code   RO         2565    i.OLED_Write_cmd    oled.o
    0x080019b8   0x080019b8   0x00000024   Code   RO         2566    i.OLED_Write_data   oled.o
    0x080019dc   0x080019dc   0x00000002   Code   RO         2515    i.OLED_task         oled_app.o
    0x080019de   0x080019de   0x00000002   Code   RO          250    i.PendSV_Handler    stm32g4xx_it.o
    0x080019e0   0x080019e0   0x00000002   Code   RO          251    i.SVC_Handler       stm32g4xx_it.o
    0x080019e2   0x080019e2   0x00000004   Code   RO          252    i.SysTick_Handler   stm32g4xx_it.o
    0x080019e6   0x080019e6   0x00000068   Code   RO           14    i.SystemClock_Config  main.o
    0x08001a4e   0x08001a4e   0x00000002   PAD
    0x08001a50   0x08001a50   0x00000010   Code   RO         2392    i.SystemInit        system_stm32g4xx.o
    0x08001a60   0x08001a60   0x00000002   Code   RO          253    i.UsageFault_Handler  stm32g4xx_it.o
    0x08001a62   0x08001a62   0x00000020   Code   RO         2264    i.__NVIC_SetPriority  stm32g4xx_hal_cortex.o
    0x08001a82   0x08001a82   0x00000016   Code   RO         2474    i.key_read          key_app.o
    0x08001a98   0x08001a98   0x0000001c   Code   RO         2475    i.key_task          key_app.o
    0x08001ab4   0x08001ab4   0x00000022   Code   RO           15    i.main              main.o
    0x08001ad6   0x08001ad6   0x00000002   PAD
    0x08001ad8   0x08001ad8   0x0000000c   Code   RO         2429    i.scheduler_init    scheduler.o
    0x08001ae4   0x08001ae4   0x0000003c   Code   RO         2430    i.scheduler_run     scheduler.o
    0x08001b20   0x08001b20   0x0000000a   Code   RO         2755    x$fpl$fpinit        fz_wm.l(fpinit.o)
    0x08001b2a   0x08001b2a   0x00000010   Data   RO         2393    .constdata          system_stm32g4xx.o
    0x08001b3a   0x08001b3a   0x00000a98   Data   RO         2567    .constdata          oled.o
    0x080025d2   0x080025d2   0x00000002   PAD
    0x080025d4   0x080025d4   0x00000020   Data   RO         2823    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x080025f4, Size: 0x00000700, Max: 0x00020000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x080025f4   0x0000000c   Data   RW          908    .data               stm32g4xx_hal.o
    0x2000000c   0x08002600   0x00000004   Data   RW         2395    .data               system_stm32g4xx.o
    0x20000010   0x08002604   0x0000001c   Data   RW         2431    .data               scheduler.o
    0x2000002c   0x08002620   0x00000004   Data   RW         2476    .data               key_app.o
    0x20000030   0x08002624   0x00000016   Data   RW         2568    .data               oled.o
    0x20000046   0x0800263a   0x00000002   PAD
    0x20000048        -       0x00000054   Zero   RW          206    .bss                i2c.o
    0x2000009c        -       0x00000060   Zero   RW         2697    .bss                c_w.l(libspace.o)
    0x200000fc   0x0800263a   0x00000004   PAD
    0x20000100        -       0x00000200   Zero   RW            2    HEAP                startup_stm32g474xx.o
    0x20000300        -       0x00000400   Zero   RW            1    STACK               startup_stm32g474xx.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

        76          6          0          0          0        883   gpio.o
       216         26          0          0         84       1765   i2c.o
        50          4          0          4          0       1600   key_app.o
       142          0          0          0          0     787276   main.o
       396         24       2712         22          0       5479   oled.o
        36          8          0          0          0       1385   oled_app.o
        72          8          0         28          0       1672   scheduler.o
        64         26        472          0       1536        848   startup_stm32g474xx.o
       162         24          0         12          0       4361   stm32g4xx_hal.o
       172         14          0          0          0      33186   stm32g4xx_hal_cortex.o
       594         34          0          0          0       2355   stm32g4xx_hal_gpio.o
      1280         22          0          0          0      10247   stm32g4xx_hal_i2c.o
       168          0          0          0          0       1923   stm32g4xx_hal_i2c_ex.o
        48          6          0          0          0        878   stm32g4xx_hal_msp.o
       204         18          0          0          0       1417   stm32g4xx_hal_pwr_ex.o
      1612         64          0          0          0       5094   stm32g4xx_hal_rcc.o
       768         10          0          0          0       1976   stm32g4xx_hal_rcc_ex.o
        20          0          0          0          0       4214   stm32g4xx_it.o
        16          4         16          4          0       1139   system_stm32g4xx.o

    ----------------------------------------------------------------------
      6114        <USER>       <GROUP>         72       1620     867698   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        18          0          2          2          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         8          0          0          0          0         68   __main.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
        18          0          0          0          0         80   exit.o
         6          0          0          0          0        152   heapauxi.o
         0          0          0          0          0          0   indicate_semi.o
         2          0          0          0          0          0   libinit.o
         6          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
        78          0          0          0          0         80   rt_memclr_w.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        12          4          0          0          0         68   sys_exit.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
        10          0          0          0          0        116   fpinit.o

    ----------------------------------------------------------------------
       368         <USER>          <GROUP>          0        100        780   Library Totals
         4          0          0          0          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

       354         16          0          0         96        664   c_w.l
        10          0          0          0          0        116   fz_wm.l

    ----------------------------------------------------------------------
       368         <USER>          <GROUP>          0        100        780   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

      6482        314       3234         72       1720     862870   Grand Totals
      6482        314       3234         72       1720     862870   ELF Image Totals
      6482        314       3234         72          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                 9716 (   9.49kB)
    Total RW  Size (RW Data + ZI Data)              1792 (   1.75kB)
    Total ROM Size (Code + RO Data + RW Data)       9788 (   9.56kB)

==============================================================================

