# STM32G474 OLED项目

## 项目概述
基于STM32G474的OLED显示项目，使用0.91寸SSD1306 OLED屏幕通过I2C接口进行通信。

## 硬件配置
- MCU: STM32G474
- OLED: 0.91寸 SSD1306 (128x32分辨率)
- I2C接口: I2C1
  - SCL: PB8
  - SDA: PB9
- I2C地址: 0x78

## 软件架构
项目采用分层架构设计：

### Hardware层 (Hardware/oled/)
- `oled.c/oled.h`: OLED硬件驱动层
- `oledfont.h`: 字体数据
- `oledpic.h`: 图片数据

### Application层 (APP/)
- `oled_app.c/oled_app.h`: OLED应用逻辑层
- `mydefine.h`: 统一配置文件

## OLED应用层API

### 初始化函数
```c
void OLED_App_Init(void);
```
- 功能: 初始化OLED硬件并清屏
- 调用时机: 系统启动后，I2C初始化完成后

### 显示函数
```c
void OLED_App_ShowHello(void);
```
- 功能: 在OLED第一行显示"hello"
- 字体: 16号字体
- 位置: 坐标(0,0)

## 使用方法

1. 在main.c中包含头文件：
```c
#include "oled_app.h"
```

2. 在初始化部分调用：
```c
OLED_App_Init();        // 初始化OLED
OLED_App_ShowHello();   // 显示hello
```

## 编译说明
- 开发环境: Keil MDK-ARM
- 项目文件: MDK-ARM/qian1.uvprojx
- 编译器: ARM Compiler 5

## 注意事项
- 确保I2C1正确初始化
- OLED电源连接正确
- I2C上拉电阻配置正确（通常内部上拉即可）

## 代码规范修复
- 修复了函数声明中缺少参数类型的警告
- 所有无参数函数现在正确使用 `void` 声明
- 添加了函数注释说明
