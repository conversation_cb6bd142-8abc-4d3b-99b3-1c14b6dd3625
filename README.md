# STM32G474 OLED项目

## 项目描述
基于STM32G474的OLED显示项目，使用I2C接口驱动0.91寸OLED显示屏(SSD1306)。

## 硬件配置
- MCU: STM32G474RET6
- OLED: 0.91寸 128x32 SSD1306
- I2C接口: I2C1
  - SCL: PB8
  - SDA: PB9

## 软件架构
```
├── Hardware/oled/          # OLED硬件驱动层
│   ├── oled.c             # OLED驱动实现
│   ├── oled.h             # OLED驱动头文件
│   ├── oledfont.h         # 字体库
│   └── oledpic.h          # 图片库
├── APP/                   # 应用层
│   ├── oled_app.c         # OLED应用逻辑
│   ├── oled_app.h         # OLED应用头文件
│   └── mydefine.h         # 统一配置文件
└── Core/                  # STM32 HAL库和系统文件
    ├── Inc/i2c.h          # I2C配置头文件
    └── Src/i2c.c          # I2C配置实现
```

## 功能特性
- OLED初始化和基本显示功能
- 在第一行显示"Hello"文字
- 支持16号和8号字体
- 支持中文显示(需要字库)
- 支持图片显示

## 使用方法

### 1. 初始化
```c
OLED_App_Init();  // 初始化OLED应用，会自动显示Hello
```

### 2. 显示文字
```c
OLED_ShowStr(x, y, "text", fontsize);  // 显示字符串
OLED_ShowChar(x, y, 'A', fontsize);    // 显示单个字符
```

### 3. 显示数字
```c
OLED_ShowNum(x, y, 123, 3, 16);        // 显示数字
OLED_ShowFloat(x, y, 3.14, 2, 16);     // 显示浮点数
```

### 4. 屏幕控制
```c
OLED_Clear();        // 清屏
OLED_Display_On();   // 开启显示
OLED_Display_Off();  // 关闭显示
```

## 编译和烧录
1. 使用Keil MDK-ARM打开项目
2. 编译项目
3. 连接ST-Link调试器
4. 烧录到STM32G474开发板

## 注意事项
- 确保I2C引脚连接正确(PB8-SCL, PB9-SDA)
- OLED电源电压为3.3V
- I2C地址为0x78(7位地址0x3C)
- 显示坐标系：x(0-127), y(0-3)，每个y代表8个像素行

## 更新日志
- 2025-01-22: 完成OLED驱动移植和应用层对接
- 支持在第一行显示"Hello"文字
- 配置I2C1接口(PB8/PB9)
